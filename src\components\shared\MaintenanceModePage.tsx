import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Wrench, Clock, LogOut } from "lucide-react";
import { useAuth } from "@/context/AuthContext";

interface MaintenanceModePageProps {
  userName?: string;
}

const MaintenanceModePage: React.FC<MaintenanceModePageProps> = ({ userName }) => {
  const { signOut } = useAuth();

  const handleSignOut = async () => {
    await signOut();
    window.location.href = "/";
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md shadow-lg border-amber-200">
        <CardHeader className="text-center border-b pb-4">
          <div className="flex justify-center mb-4">
            <div className="h-20 w-20 rounded-full bg-amber-100 flex items-center justify-center">
              <Wrench className="h-10 w-10 text-amber-500" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-amber-600">Maintenance Mode</CardTitle>
        </CardHeader>
        <CardContent className="pt-6 text-center space-y-4">
          <p className="text-lg">
            {userName ? `Hello ${userName}, ` : ""}The system is currently undergoing maintenance.
          </p>
          <div className="bg-blue-50 p-4 rounded-md border border-blue-200 flex items-start">
            <Clock className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-blue-800">
              We're working to improve your experience. Please check back later or contact your administrator for more information.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center pt-2 pb-6">
          <Button variant="outline" className="w-full" onClick={handleSignOut}>
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default MaintenanceModePage;

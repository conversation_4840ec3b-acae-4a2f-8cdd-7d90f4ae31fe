import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import TabletQRDisplay from "@/components/tablet/TabletQRDisplay";
import SimpleTabletDisplay from "@/components/tablet/SimpleTabletDisplay";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { Tablet, Settings, Wifi } from "lucide-react";

interface Block {
  id: string;
  name: string;
  school_id: string;
}

interface Room {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  block_id: string;
}

export default function TabletDisplay() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [isConfigured, setIsConfigured] = useState(false);
  const [showConfig, setShowConfig] = useState(false);

  // Configuration state
  const [schoolId, setSchoolId] = useState(searchParams.get("school") || "");
  const [blockId, setBlockId] = useState(searchParams.get("block") || "");
  const [roomId, setRoomId] = useState(searchParams.get("room") || "");

  // Data state
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(false);

  // Check if tablet is configured
  useEffect(() => {
    const configured = schoolId && roomId; // blockId is optional
    setIsConfigured(configured);

    if (configured) {
      // Update URL params
      const params: Record<string, string> = { school: schoolId, room: roomId };
      if (blockId) params.block = blockId;
      setSearchParams(params);

      // Mark this as a tablet session to prevent redirects
      sessionStorage.setItem("isTabletSession", "true");
      sessionStorage.setItem(
        "tabletConfig",
        JSON.stringify({
          schoolId,
          blockId,
          roomId,
          timestamp: Date.now(),
        })
      );
    }
  }, [schoolId, blockId, roomId, setSearchParams]);

  // Fetch blocks when school changes
  useEffect(() => {
    if (schoolId) {
      fetchBlocks();
    }
  }, [schoolId]);

  // Fetch rooms when block changes
  useEffect(() => {
    if (blockId) {
      fetchRooms();
    }
  }, [blockId]);

  const fetchBlocks = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("blocks")
        .select("*")
        .eq("school_id", schoolId)
        .order("name");

      if (error) throw error;
      setBlocks(data || []);
    } catch (error) {
      console.error("Error fetching blocks:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchRooms = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("rooms")
        .select("*")
        .eq("block_id", blockId)
        .order("name");

      if (error) throw error;
      setRooms(data || []);
    } catch (error) {
      console.error("Error fetching rooms:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConfiguration = () => {
    if (schoolId && blockId && roomId) {
      setIsConfigured(true);
      setShowConfig(false);

      // Save to localStorage for persistence
      localStorage.setItem(
        "tablet_config",
        JSON.stringify({
          schoolId,
          blockId,
          roomId,
          configuredAt: new Date().toISOString(),
        })
      );
    }
  };

  // Load configuration from URL params or saved config on mount
  useEffect(() => {
    // First try to load from URL parameters
    const urlSchool = searchParams.get("school");
    const urlBlock = searchParams.get("block");
    const urlRoom = searchParams.get("room");

    if (urlSchool && urlRoom) {
      // Load from URL parameters (priority)
      setSchoolId(urlSchool);
      setBlockId(urlBlock || "");
      setRoomId(urlRoom);

      // Mark as tablet session
      sessionStorage.setItem("isTabletSession", "true");
    } else {
      // Fallback to saved configuration
      const savedConfig = localStorage.getItem("tablet_config");
      if (savedConfig) {
        try {
          const config = JSON.parse(savedConfig);
          setSchoolId(config.schoolId);
          setBlockId(config.blockId);
          setRoomId(config.roomId);
        } catch (error) {
          console.error("Error loading saved config:", error);
        }
      }
    }
  }, [searchParams]);

  // Configuration screen
  if (!isConfigured || showConfig) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-6 h-6" />
              Tablet Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* School ID */}
            <div className="space-y-2">
              <Label htmlFor="school">School ID</Label>
              <Input
                id="school"
                placeholder="Enter school ID"
                value={schoolId}
                onChange={(e) => setSchoolId(e.target.value)}
              />
            </div>

            {/* Block Selection */}
            {schoolId && (
              <div className="space-y-2">
                <Label htmlFor="block">Block</Label>
                <Select value={blockId} onValueChange={setBlockId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a block" />
                  </SelectTrigger>
                  <SelectContent>
                    {blocks.map((block) => (
                      <SelectItem key={block.id} value={block.id}>
                        Block {block.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Room Selection */}
            {blockId && (
              <div className="space-y-2">
                <Label htmlFor="room">Room</Label>
                <Select value={roomId} onValueChange={setRoomId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a room" />
                  </SelectTrigger>
                  <SelectContent>
                    {rooms.map((room) => (
                      <SelectItem key={room.id} value={room.id}>
                        {room.name}
                        {room.building && ` - ${room.building}`}
                        {room.floor && ` Floor ${room.floor}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Save Button */}
            <Button
              onClick={handleSaveConfiguration}
              className="w-full"
              disabled={!schoolId || !blockId || !roomId || loading}
            >
              {loading ? "Loading..." : "Save Configuration"}
            </Button>

            {/* Instructions */}
            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
              <p className="font-medium mb-1">Setup Instructions:</p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Enter your school ID</li>
                <li>Select the block where this tablet is located</li>
                <li>Select the specific room</li>
                <li>Save configuration</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Main tablet display
  return (
    <div className="relative">
      {/* Configuration button (floating) */}
      <Button
        onClick={() => setShowConfig(true)}
        className="fixed top-4 left-4 z-50"
        variant="outline"
        size="sm"
      >
        <Settings className="w-4 h-4" />
      </Button>

      {/* QR Display */}
      <SimpleTabletDisplay roomId={roomId} schoolId={schoolId} />
    </div>
  );
}

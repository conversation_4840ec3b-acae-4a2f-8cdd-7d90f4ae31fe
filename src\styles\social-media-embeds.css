/* Enhanced Social Media Embed Styles */

/* Facebook Embed Styling */
.facebook-embed-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  position: relative;
}

.facebook-embed-container .fb-page {
  width: 100% !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.facebook-embed-container iframe {
  border: none !important;
  border-radius: 0 !important;
  width: 100% !important;
  min-height: 600px !important;
  height: 600px !important;
  background: transparent !important;
}

/* Twitter Embed Styling */
.twitter-embed-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  position: relative;
  padding: 0 !important;
}

.twitter-embed-container .twitter-timeline {
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
  width: 100% !important;
  height: 500px !important;
}

.twitter-embed-container iframe {
  border: none !important;
  border-radius: 0 !important;
  width: 100% !important;
  height: 500px !important;
  background: transparent !important;
}

/* YouTube Embed Styling */
.youtube-embed-container {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  position: relative;
  aspect-ratio: 16/9;
}

.youtube-embed-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
}

/* YouTube Responsive Template Styling */
.youtube-responsive-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.youtube-responsive-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
}

/* YouTube Videos Grid Layout */
.youtube-videos-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-radius: 0 0 12px 12px;
  min-height: auto;
  height: auto;
  max-height: 600px;
  overflow-y: auto;
}

/* Desktop: Show 2 videos per row for larger screens */
@media (min-width: 1024px) {
  .youtube-videos-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
    max-height: 800px;
  }
}

/* Tablet: Show 2 videos per row for medium screens */
@media (min-width: 768px) and (max-width: 1023px) {
  .youtube-videos-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    max-height: 700px;
  }
}

.youtube-video-item {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.youtube-video-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.youtube-video-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 8px !important;
  background: transparent !important;
}

.youtube-video-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  padding: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  border-radius: 0 0 8px 8px;
}

/* YouTube Container Specific Styling */
.youtube-embed-container {
  min-height: auto !important;
  height: auto !important;
  overflow: visible !important;
  max-height: none !important;
}



/* YouTube Embed Wrapper Styling */
.youtube-embed-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-radius: 0 0 12px 12px;
}

.youtube-embed-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
}

/* YouTube Error Display */
.youtube-error-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 400px;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-radius: 0 0 12px 12px;
}

.youtube-error-content {
  text-align: center;
  padding: 2rem;
  color: #dc2626;
}

.youtube-error-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
}

.youtube-error-content p {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #7f1d1d;
}

.youtube-error-content small {
  font-size: 0.875rem;
  color: #991b1b;
  opacity: 0.8;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .facebook-embed-container {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }
  
  .twitter-embed-container {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }
  
  .youtube-embed-container {
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  }
}

/* Custom scrollbar for embedded content */
.facebook-embed-container *::-webkit-scrollbar,
.twitter-embed-container *::-webkit-scrollbar {
  width: 6px;
}

.facebook-embed-container *::-webkit-scrollbar-track,
.twitter-embed-container *::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.facebook-embed-container *::-webkit-scrollbar-thumb,
.twitter-embed-container *::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.facebook-embed-container *::-webkit-scrollbar-thumb:hover,
.twitter-embed-container *::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* YouTube Grid Scrollbar Styling */
.youtube-videos-grid::-webkit-scrollbar {
  width: 8px;
}

.youtube-videos-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.youtube-videos-grid::-webkit-scrollbar-thumb {
  background: rgba(239, 68, 68, 0.6);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.youtube-videos-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(239, 68, 68, 0.8);
}

/* Loading animation overlay */
.social-embed-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced hover effects */
.social-media-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-media-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Platform-specific accent colors */
.platform-facebook {
  border-left: 4px solid #1877f2;
}

.platform-twitter {
  border-left: 4px solid #1da1f2;
}

.platform-youtube {
  border-left: 4px solid #ff0000;
}

.platform-instagram {
  border-left: 4px solid #e4405f;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .facebook-embed-container,
  .twitter-embed-container {
    min-height: 600px !important;
  }

  .facebook-embed-container iframe,
  .twitter-embed-container iframe {
    min-height: 600px !important;
    height: 600px !important;
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .facebook-embed-container,
  .twitter-embed-container {
    min-height: 600px !important;
  }

  .facebook-embed-container iframe,
  .twitter-embed-container iframe {
    min-height: 600px !important;
    height: 600px !important;
    width: 100% !important;
  }

  .youtube-embed-container {
    aspect-ratio: 16/10;
    min-height: 250px;
  }

  .youtube-embed-container iframe {
    width: 100% !important;
    height: 100% !important;
  }

  .youtube-responsive-wrapper,
  .youtube-embed-wrapper {
    aspect-ratio: 16/10;
    min-height: 250px;
  }

  /* YouTube Videos Grid Responsive */
  .youtube-videos-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .youtube-video-item {
    aspect-ratio: 16/10;
  }
}

@media (max-width: 640px) {
  .facebook-embed-container,
  .twitter-embed-container {
    min-height: 600px !important;
  }

  .facebook-embed-container iframe,
  .twitter-embed-container iframe {
    min-height: 600px !important;
    height: 600px !important;
    width: 100% !important;
  }

  .youtube-embed-container {
    aspect-ratio: 16/11;
    min-height: 220px;
  }

  .youtube-responsive-wrapper,
  .youtube-embed-wrapper {
    aspect-ratio: 16/11;
    min-height: 220px;
  }

  /* YouTube Videos Grid Mobile */
  .youtube-videos-grid {
    gap: 0.5rem;
    padding: 0.5rem;
    max-height: 500px;
    overflow-y: auto;
  }

  .youtube-video-item {
    aspect-ratio: 16/11;
  }

  .youtube-video-label {
    font-size: 0.7rem;
    padding: 0.375rem;
  }
}

@media (max-width: 480px) {
  .facebook-embed-container,
  .twitter-embed-container {
    min-height: 600px !important;
  }

  .facebook-embed-container iframe,
  .twitter-embed-container iframe {
    min-height: 600px !important;
    height: 600px !important;
    width: 100% !important;
  }

  .youtube-embed-container {
    aspect-ratio: 16/12;
    min-height: 200px;
  }

  .youtube-responsive-wrapper,
  .youtube-embed-wrapper {
    aspect-ratio: 16/12;
    min-height: 200px;
  }
}

@media (max-width: 375px) {
  .facebook-embed-container,
  .twitter-embed-container {
    min-height: 600px !important;
  }

  .facebook-embed-container iframe,
  .twitter-embed-container iframe {
    min-height: 600px !important;
    height: 600px !important;
    width: 100% !important;
  }

  .youtube-embed-container {
    aspect-ratio: 4/3;
    min-height: 180px;
  }

  .youtube-responsive-wrapper,
  .youtube-embed-wrapper {
    aspect-ratio: 4/3;
    min-height: 180px;
  }
}

/* Beautiful gradient borders */
.gradient-border {
  position: relative;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  padding: 2px;
  border-radius: 16px;
}

.gradient-border-content {
  background: white;
  border-radius: 14px;
  position: relative;
  z-index: 1;
}

/* Dark mode gradient borders */
@media (prefers-color-scheme: dark) {
  .gradient-border-content {
    background: #1f2937;
  }
}

/* Pulse animation for live indicators */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced typography for social content */
.social-content h1,
.social-content h2,
.social-content h3 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

.social-content p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

.social-content a {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s;
}

.social-content a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Beautiful loading states */
.embed-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom focus styles for accessibility */
.social-media-card:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Shimmer animation for loading states */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Mobile-first responsive design improvements */
.social-media-card {
  margin: 0 auto;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 640px) {
  .social-media-card {
    margin: 0;
    border-radius: 0.75rem;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  .social-media-card .gradient-border {
    padding: 1px;
    border-radius: 0.75rem;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .social-media-card .gradient-border-content {
    border-radius: 0.6875rem;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Ensure embeds are always responsive */
.facebook-embed-container *,
.twitter-embed-container *,
.youtube-embed-container * {
  max-width: 100% !important;
  box-sizing: border-box !important;
  overflow-x: hidden !important;
}

/* Force responsive behavior for stubborn embeds */
.facebook-embed-container .fb-page,
.twitter-embed-container .twitter-timeline {
  max-width: 100% !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

/* Specific fix for screens smaller than 625px */
@media (max-width: 625px) {
  .facebook-embed-container,
  .twitter-embed-container {
    overflow-x: auto !important;
    overflow-y: hidden !important;
    -webkit-overflow-scrolling: touch;
  }

  .facebook-embed-container .fb-page {
    min-width: 320px !important;
  }

  .twitter-embed-container .twitter-timeline {
    min-width: 320px !important;
  }

  /* YouTube responsive fixes for small screens */
  .youtube-embed-container,
  .youtube-responsive-wrapper,
  .youtube-embed-wrapper {
    aspect-ratio: 16/10 !important;
    min-height: 200px !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .youtube-responsive-iframe,
  .youtube-embed-wrapper iframe {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
  }
}

/* Additional mobile width constraints */
@media (max-width: 768px) {
  .facebook-embed-container,
  .twitter-embed-container,
  .youtube-embed-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  .facebook-embed-container iframe,
  .twitter-embed-container iframe,
  .youtube-embed-container iframe {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }
}

/* Mobile touch improvements */
@media (max-width: 768px) {
  .social-media-card:hover {
    transform: none;
  }

  .social-media-card:active {
    transform: scale(0.98);
  }
}

/* Improve text readability on small screens */
@media (max-width: 480px) {
  .social-content {
    font-size: 14px;
    line-height: 1.5;
  }

  .social-content h1,
  .social-content h2,
  .social-content h3 {
    font-size: 1.1em;
    line-height: 1.3;
  }
}

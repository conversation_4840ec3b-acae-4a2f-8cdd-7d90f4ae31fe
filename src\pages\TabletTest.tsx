/**
 * 🧪 Tablet Test Page
 * Simple test page to verify tablet setup functionality
 */

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Tablet, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  ExternalLink,
  Copy,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { tabletAuthService } from "@/lib/services/tablet-auth";
import { supabase } from "@/integrations/supabase/client";

export default function TabletTest() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [schools, setSchools] = useState<any[]>([]);
  const [rooms, setRooms] = useState<any[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    fetchTestData();
  }, []);

  const fetchTestData = async () => {
    try {
      // Fetch schools
      const { data: schoolsData, error: schoolsError } = await supabase
        .from("schools")
        .select("id, name")
        .limit(5);

      if (schoolsError) throw schoolsError;
      setSchools(schoolsData || []);

      // Fetch rooms
      const { data: roomsData, error: roomsError } = await supabase
        .from("rooms")
        .select("id, name, school_id")
        .limit(5);

      if (roomsError) throw roomsError;
      setRooms(roomsData || []);
    } catch (error) {
      console.error("Error fetching test data:", error);
    }
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    const results: any[] = [];

    // Test 1: Database Connection
    try {
      const { data, error } = await supabase.from("schools").select("count").limit(1);
      results.push({
        test: "Database Connection",
        status: error ? "failed" : "passed",
        message: error ? error.message : "Successfully connected to Supabase",
        details: { data, error }
      });
    } catch (error) {
      results.push({
        test: "Database Connection",
        status: "failed",
        message: "Failed to connect to database",
        details: { error }
      });
    }

    // Test 2: Tablet Authentication Service
    try {
      const deviceInfo = tabletAuthService.getCurrentDevice();
      results.push({
        test: "Tablet Auth Service",
        status: "passed",
        message: "Tablet authentication service loaded successfully",
        details: { deviceInfo }
      });
    } catch (error) {
      results.push({
        test: "Tablet Auth Service",
        status: "failed",
        message: "Failed to load tablet authentication service",
        details: { error }
      });
    }

    // Test 3: Device Fingerprinting
    try {
      const fingerprint = tabletAuthService.generateDeviceFingerprint();
      results.push({
        test: "Device Fingerprinting",
        status: fingerprint ? "passed" : "failed",
        message: fingerprint ? `Generated fingerprint: ${fingerprint.substring(0, 16)}...` : "Failed to generate fingerprint",
        details: { fingerprint }
      });
    } catch (error) {
      results.push({
        test: "Device Fingerprinting",
        status: "failed",
        message: "Error generating device fingerprint",
        details: { error }
      });
    }

    // Test 4: Tablet Registration (if we have test data)
    if (schools.length > 0 && rooms.length > 0) {
      try {
        const testSchool = schools[0];
        // Find a room that actually belongs to the test school
        const testRoom = rooms.find(r => r.school_id === testSchool.id);

        if (!testRoom) {
          results.push({
            test: "Tablet Registration",
            status: "failed",
            message: "No room found for the test school",
            details: { testSchool, availableRooms: rooms.map(r => ({ id: r.id, name: r.name, school_id: r.school_id })) }
          });
        } else {
          const result = await tabletAuthService.registerTablet(
            testSchool.id,
            testRoom.id,
            undefined,
            "Test Tablet"
          );

          results.push({
            test: "Tablet Registration",
            status: result.success ? "passed" : "failed",
            message: result.success ? "Successfully registered test tablet" : result.error || "Registration failed",
            details: { result, testSchool, testRoom }
          });
        }
      } catch (error) {
        results.push({
          test: "Tablet Registration",
          status: "failed",
          message: "Error during tablet registration",
          details: { error }
        });
      }
    }

    setTestResults(results);
    setIsRunning(false);

    // Show summary toast
    const passed = results.filter(r => r.status === "passed").length;
    const total = results.length;
    
    toast({
      title: `Tests Complete: ${passed}/${total} Passed`,
      description: passed === total ? "All tests passed! 🎉" : "Some tests failed. Check details below.",
      variant: passed === total ? "default" : "destructive"
    });
  };

  const generateTestURL = () => {
    if (schools.length === 0 || rooms.length === 0) return "";
    
    const testSchool = schools[0];
    const testRoom = rooms.find(r => r.school_id === testSchool.id) || rooms[0];
    
    return `${window.location.origin}/tablet?school=${testSchool.id}&room=${testRoom.id}&setup=auto&name=Test%20Tablet`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "URL copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "passed": return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "failed": return <XCircle className="w-5 h-5 text-red-600" />;
      default: return <AlertCircle className="w-5 h-5 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "passed": return "text-green-600 bg-green-50 border-green-200";
      case "failed": return "text-red-600 bg-red-50 border-red-200";
      default: return "text-yellow-600 bg-yellow-50 border-yellow-200";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-2xl">
              <Tablet className="w-6 h-6 text-blue-600" />
              Tablet System Test
            </CardTitle>
            <p className="text-gray-600">
              Test the tablet authentication and setup functionality
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Button 
                onClick={runTests} 
                disabled={isRunning}
                size="lg"
              >
                {isRunning ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="w-4 h-4 mr-2" />
                )}
                {isRunning ? "Running Tests..." : "Run Tests"}
              </Button>
              
              {generateTestURL() && (
                <div className="flex gap-2">
                  <Button 
                    variant="outline"
                    onClick={() => window.open(generateTestURL(), '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Test Setup URL
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => copyToClipboard(generateTestURL())}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {testResults.map((result, index) => (
                <div 
                  key={index}
                  className={`border rounded-lg p-4 ${getStatusColor(result.status)}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      <h3 className="font-semibold">{result.test}</h3>
                    </div>
                    <Badge variant="outline" className={getStatusColor(result.status)}>
                      {result.status.toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-sm mb-2">{result.message}</p>
                  {result.details && (
                    <details className="text-xs">
                      <summary className="cursor-pointer font-medium">Details</summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Test Data Info */}
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Available Test Data</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Schools ({schools.length})</h4>
              <div className="space-y-1">
                {schools.slice(0, 3).map(school => (
                  <div key={school.id} className="text-sm text-gray-600">
                    • {school.name} ({school.id.substring(0, 8)}...)
                  </div>
                ))}
                {schools.length > 3 && (
                  <div className="text-sm text-gray-500">
                    ... and {schools.length - 3} more
                  </div>
                )}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Rooms ({rooms.length})</h4>
              <div className="space-y-1">
                {rooms.slice(0, 3).map(room => (
                  <div key={room.id} className="text-sm text-gray-600">
                    • {room.name} ({room.id.substring(0, 8)}...)
                  </div>
                ))}
                {rooms.length > 3 && (
                  <div className="text-sm text-gray-500">
                    ... and {rooms.length - 3} more
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

import { UnifiedToaster } from "@/components/ui/unified-toast";
import { TooltipProvider } from "@/components/ui/tooltip";
import { OfflineAlert } from "@/components/ui/offline-alert";
import SupabaseConnectionAlert from "./components/shared/SupabaseConnectionAlert";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { SchoolProvider } from "./context/SchoolContext";
import { SchoolThemeProvider } from "./components/providers/SchoolThemeProvider";
import { ThemeProvider } from "./components/providers/ThemeProvider";
import { LanguageProvider } from "./context/SimpleLanguageContext";
import { TranslationUpdater } from "./components/providers/TranslationUpdater";
import MaintenanceRedirect from "./components/shared/MaintenanceRedirect";
import UserStatusRedirect from "./components/shared/UserStatusRedirect";
import { getUIText } from "./config/branding";
import "./i18n";
import Index from "./pages/Index";
import Login from "./pages/Login";
import SignUp from "./pages/SignUp";
import SystemAdminSignUp from "./pages/SystemAdminSignUp";
import Student from "./pages/Student";
import Teacher from "./pages/Teacher";
import Admin from "./pages/Admin";
import SystemAdmin from "./pages/SystemAdmin";
import AdminProfileSetup from "./pages/AdminProfileSetup";
import NotFound from "./pages/NotFound";
import Offline from "./pages/Offline";
import RunMigrations from "./pages/RunMigrations";
import TabletDisplay from "./pages/TabletDisplay";
import TabletSetup from "./pages/TabletSetup";
import TabletTest from "./pages/TabletTest";
import QRDebugTest from "./pages/QRDebugTest";
import ModernTabletDisplay from "./components/tablet/ModernTabletDisplay";
import { useAuth } from "./context/AuthContext";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { excuseCleanupService } from "@/lib/services/excuse-cleanup-service";
import { useTranslation } from "react-i18next";

const queryClient = new QueryClient();

// Component to handle protected routes
const ProtectedRoute = ({
  children,
  requiredRole,
}: {
  children: React.ReactNode;
  requiredRole?: "student" | "teacher" | "admin";
}) => {
  const { profile, loading } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    if (!loading) {
      if (!profile) {
        // If not logged in, redirect to login
        navigate("/login", { replace: true });
      } else if (requiredRole && profile.role !== requiredRole) {
        // If user doesn't have the required role, redirect to their appropriate page
        navigate(`/${profile.role}`, { replace: true });
      }
    }
  }, [loading, profile, requiredRole, navigate]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner message={t("loading.authenticating")} size="lg" />
      </div>
    );
  }

  return profile ? <>{children}</> : null;
};

const AppRoutes = () => {
  const { profile, loading } = useAuth();
  const location = useLocation();

  // Check if this is a tablet route - bypass auth for tablets
  const isTabletRoute = location.pathname.startsWith("/tablet");

  if (loading && !isTabletRoute) {
    // Get loading message based on stored language preference, fallback to browser language
    const storedLang = localStorage.getItem("i18nextLng");
    const browserLang = navigator.language.startsWith("tr") ? "tr" : "en";
    const targetLang = storedLang === "tr" ? "tr" : storedLang === "en" ? "en" : browserLang;
    const uiText = getUIText(targetLang);

    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner message={uiText.LOADING} size="lg" />
      </div>
    );
  }

  return (
    <Routes>
      <Route
        path="/"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <Index />
          )
        }
      />
      <Route
        path="/login"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <Login />
          )
        }
      />
      <Route
        path="/signup"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <SignUp />
          )
        }
      />
      <Route
        path="/system-admin-signup"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <SystemAdminSignUp />
          )
        }
      />
      <Route
        path="/student"
        element={
          <ProtectedRoute requiredRole="student">
            <Student />
          </ProtectedRoute>
        }
      />
      <Route
        path="/teacher"
        element={
          <ProtectedRoute requiredRole="teacher">
            <Teacher />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin"
        element={
          <ProtectedRoute requiredRole="admin">
            <Admin />
          </ProtectedRoute>
        }
      />
      <Route
        path="/system-admin"
        element={
          <ProtectedRoute requiredRole="admin">
            {/* Additional check for system admin access level is in the SystemAdmin component */}
            <SystemAdmin />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin-profile-setup"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminProfileSetup />
          </ProtectedRoute>
        }
      />
      <Route path="/tablet" element={<ModernTabletDisplay />} />
      <Route path="/tablet-legacy" element={<TabletDisplay />} />
      <Route
        path="/tablet-setup"
        element={
          <ProtectedRoute requiredRole="admin">
            <TabletSetup />
          </ProtectedRoute>
        }
      />
      <Route
        path="/tablet-test"
        element={
          <ProtectedRoute requiredRole="admin">
            <TabletTest />
          </ProtectedRoute>
        }
      />
      <Route
        path="/qr-debug"
        element={
          <ProtectedRoute requiredRole="admin">
            <QRDebugTest />
          </ProtectedRoute>
        }
      />
      <Route path="/offline" element={<Offline />} />
      <Route path="/run-migrations" element={<RunMigrations />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

// Component to conditionally wrap with auth checks
const ConditionalAuthWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const location = useLocation();
  const isTabletRoute = location.pathname.startsWith("/tablet");

  if (isTabletRoute) {
    // For tablet routes, bypass auth checks
    return <>{children}</>;
  }

  // For all other routes, use normal auth flow
  return (
    <MaintenanceRedirect>
      <UserStatusRedirect>{children}</UserStatusRedirect>
    </MaintenanceRedirect>
  );
};

const App = () => {
  // Initialize excuse cleanup service when app starts
  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Start excuse cleanup service
        await excuseCleanupService.startAutomaticCleanup();
        console.log("✅ Excuse cleanup service initialized");
      } catch (error) {
        console.error("❌ Error initializing excuse cleanup service:", error);
      }
    };

    initializeServices();

    // Cleanup on unmount
    return () => {
      excuseCleanupService.stopAutomaticCleanup();
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter
        future={{ v7_startTransition: true, v7_relativeSplatPath: true }}
      >
        <AuthProvider>
          <LanguageProvider>
            <TranslationUpdater>
              <SchoolProvider>
                <ThemeProvider
                  defaultTheme="dark"
                  storageKey="campus-guardian-theme"
                >
                  <SchoolThemeProvider>
                    <TooltipProvider>
                      <OfflineAlert />
                      {/* Use our safe toast provider */}
                      <UnifiedToaster />
                      <SupabaseConnectionAlert />
                      <ConditionalAuthWrapper>
                        <div className="flex flex-col min-h-screen">
                          <div className="flex-1">
                            <AppRoutes />
                          </div>
                          {/* Footer will be conditionally rendered in each page */}
                        </div>
                      </ConditionalAuthWrapper>
                    </TooltipProvider>
                  </SchoolThemeProvider>
                </ThemeProvider>
              </SchoolProvider>
            </TranslationUpdater>
          </LanguageProvider>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;

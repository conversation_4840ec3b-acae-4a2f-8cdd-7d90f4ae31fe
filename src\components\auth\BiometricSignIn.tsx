import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Fingerprint,
  Shield,
  CheckCircle,
  XCircle,
  Loader2,
  AlertTriangle,
  Sparkles,
  Zap,
  Eye,
  Scan
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { isWebAuthnAvailable } from "@/lib/webauthn";
import BiometricAuthService from "@/lib/services/biometric-auth-service";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";

interface BiometricSignInProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

export default function BiometricSignIn({ onSuccess, onError, className }: BiometricSignInProps) {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'checking' | 'authenticating' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState("");
  const [progress, setProgress] = useState(0);
  const [hasBiometric, setHasBiometric] = useState(false);
  const [isSupported, setIsSupported] = useState(false);

  const { signInWithBiometrics } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    setIsSupported(isWebAuthnAvailable());
  }, []);

  // Check if user has biometric auth when email changes
  useEffect(() => {
    const checkBiometric = async () => {
      if (email && email.includes('@')) {
        setStatus('checking');
        const hasAuth = await BiometricAuthService.hasBiometricAuth(email);
        setHasBiometric(hasAuth);
        setStatus('idle');
      } else {
        setHasBiometric(false);
      }
    };

    const timeoutId = setTimeout(checkBiometric, 500);
    return () => clearTimeout(timeoutId);
  }, [email]);

  const handleBiometricSignIn = async () => {
    if (!email || !hasBiometric) return;

    try {
      setIsLoading(true);
      setStatus('authenticating');
      setProgress(0);
      setStatusMessage(t('biometrics.preparingAuthentication'));

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 15;
        });
      }, 300);

      setStatusMessage(t('biometrics.touchSensorMessage'));
      
      await signInWithBiometrics(email);
      
      clearInterval(progressInterval);
      setProgress(100);
      setStatus('success');
      setStatusMessage(t('biometrics.authenticationSuccessMessage'));

      toast({
        title: t('biometrics.biometricSignInSuccessful'),
        description: t('biometrics.welcomeBackSecure'),
      });

      onSuccess?.();

    } catch (error: any) {
      setStatus('error');
      setStatusMessage(error.message || t('biometrics.authenticationFailed'));

      toast({
        title: t('biometrics.biometricSignInFailed'),
        description: error.message || t('biometrics.tryAgainOrUseEmail'),
        variant: "destructive",
      });

      onError?.(error.message);
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setStatus('idle');
        setProgress(0);
        setStatusMessage('');
      }, 3000);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'checking':
        return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
      case 'authenticating':
        return <Scan className="w-5 h-5 text-purple-500 animate-pulse" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Fingerprint className="w-5 h-5 text-blue-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'checking':
        return 'border-blue-200 bg-blue-50';
      case 'authenticating':
        return 'border-purple-200 bg-purple-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  if (!isSupported) {
    return (
      <Card className={cn("border-amber-200 bg-amber-50", className)}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-5 h-5 text-amber-600" />
            <div>
              <p className="text-sm font-medium text-amber-800">
                {t('biometrics.notSupported')}
              </p>
              <p className="text-xs text-amber-600">
                {t('biometrics.useEmailPassword')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                <Fingerprint className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">
                {t('biometrics.biometricSignIn')}
              </h3>
              <Sparkles className="w-4 h-4 text-yellow-500" />
            </div>
            <p className="text-sm text-gray-600">
              {t('biometrics.signInSecurely')}
            </p>
          </div>

          {/* Email Input */}
          <div className="space-y-2">
            <Label htmlFor="biometric-email" className="text-sm font-medium">
              {t('common.email')}
            </Label>
            <div className="relative">
              <Input
                id="biometric-email"
                type="email"
                placeholder={t('auth.emailPlaceholder')}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
                className="pr-10"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {getStatusIcon()}
              </div>
            </div>
            
            {/* Biometric Status */}
            <AnimatePresence>
              {email && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="overflow-hidden"
                >
                  <div className={cn("p-2 rounded-lg border", getStatusColor())}>
                    <div className="flex items-center gap-2">
                      {hasBiometric ? (
                        <>
                          <Shield className="w-4 h-4 text-green-600" />
                          <span className="text-sm font-medium text-green-800">
                            {t('biometrics.authenticationAvailable')}
                          </span>
                          <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                            <Zap className="w-3 h-3 mr-1" />
                            {t('biometrics.ready')}
                          </Badge>
                        </>
                      ) : status === 'checking' ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                          <span className="text-sm text-blue-800">
                            {t('biometrics.checkingAvailability')}
                          </span>
                        </>
                      ) : (
                        <>
                          <XCircle className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-600">
                            {t('biometrics.noBiometricSetup')}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Progress Bar */}
          <AnimatePresence>
            {isLoading && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2"
              >
                <Progress value={progress} className="h-2" />
                <p className="text-xs text-center text-gray-600">
                  {statusMessage}
                </p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Sign In Button */}
          <Button
            onClick={handleBiometricSignIn}
            disabled={!email || !hasBiometric || isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {t('auth.authenticating')}
              </>
            ) : (
              <>
                <Fingerprint className="w-4 h-4 mr-2" />
                {t('biometrics.signInWithBiometrics')}
              </>
            )}
          </Button>

          {/* Features */}
          <div className="grid grid-cols-3 gap-2 pt-2">
            <div className="text-center">
              <div className="w-8 h-8 mx-auto mb-1 bg-blue-100 rounded-full flex items-center justify-center">
                <Zap className="w-4 h-4 text-blue-600" />
              </div>
              <p className="text-xs text-gray-600">{t('biometrics.fast')}</p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 mx-auto mb-1 bg-green-100 rounded-full flex items-center justify-center">
                <Shield className="w-4 h-4 text-green-600" />
              </div>
              <p className="text-xs text-gray-600">{t('biometrics.secure')}</p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 mx-auto mb-1 bg-purple-100 rounded-full flex items-center justify-center">
                <Eye className="w-4 h-4 text-purple-600" />
              </div>
              <p className="text-xs text-gray-600">{t('biometrics.private')}</p>
            </div>
          </div>

          {/* Info Note */}
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-xs text-blue-800 text-center">
              <strong>{t('common.note')}:</strong> {t('biometrics.mustRegisterFirst')}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

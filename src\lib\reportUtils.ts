import { AttendanceRecord } from "./types";
import { format } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";

// Translation function type
type TranslationFunction = (key: string, options?: any) => string;

// Get app name from environment variable based on language
const getAppName = (t?: TranslationFunction) => {
  // Check if we're using Turkish translations
  if (t && t("common.languageCode") === "tr") {
    return (
      import.meta.env.VITE_APP_NAME_TR ||
      import.meta.env.VITE_APP_NAME ||
      "Devam Takip Sistemi"
    );
  }
  return import.meta.env.VITE_APP_NAME || "Attendance Tracking System";
};

// Helper function to format date with locale support
const formatDate = (date: string, t?: TranslationFunction) => {
  const locale = t && t("common.languageCode") === "tr" ? tr : enUS;
  return format(new Date(date), "MMM dd, yyyy HH:mm", { locale });
};

// Special Turkish date formatter for the creation timestamp
const formatTurkishCreationDate = (date: Date, t?: TranslationFunction) => {
  if (t && t("common.languageCode") === "tr") {
    // Turkish format: "Haziran 03. 2025, saat 7. 48'de"
    const day = format(date, "dd", { locale: tr });
    const month = format(date, "MMMM", { locale: tr });
    const year = format(date, "yyyy", { locale: tr });
    const hour = format(date, "H", { locale: tr });
    const minute = format(date, "mm", { locale: tr });
    return `${month} ${day}. ${year}, saat ${hour}. ${minute}'de`;
  } else {
    return format(date, "MMMM dd, yyyy 'at' h:mm a", { locale: enUS });
  }
};

// Fix Turkish character encoding for PDF
const fixTurkishText = (text: string): string => {
  if (!text) return text;

  // First normalize the text
  let fixedText = text.normalize("NFC");

  // Fix the specific Turkish character mapping issues you identified:
  // ğ → empty/not shown, ı → 1, ş → _, İ → 0
  fixedText = fixedText
    // Remove any extra spaces between characters
    .replace(/\s+/g, " ")
    .trim()
    // Fix specific Turkish words with character mapping issues
    .replace(/Olu_turulma/g, "Oluşturulma") // Fix ş → _ in creation date
    .replace(/Oluturulma/g, "Oluşturulma") // Fix missing ş
    .replace(/Örenc1\s*Ad1/g, "Öğrenci Adı") // Fix "Öğrenci Adı" (ı → 1)
    .replace(/Ö\s*renc1/g, "Öğrenci") // Fix "Öğrenci" with spacing
    .replace(/Örenc1/g, "Öğrenci") // Fix "Öğrenci" (ı → 1)
    .replace(/Ad1/g, "Adı") // Fix "Adı" (ı → 1)
    .replace(/S1n1f/g, "Sınıf") // Fix "Sınıf" (ı → 1)
    .replace(/S1n0f/g, "Sınıf") // Fix "Sınıf" (İ → 0)
    .replace(/S\s*1\s*n\s*1\s*f/g, "Sınıf") // Fix "Sınıf" with spacing
    .replace(/Tar1h/g, "Tarih") // Fix "Tarih" (ı → 1)
    .replace(/Do_rulama/g, "Doğrulama") // Fix "Doğrulama" (ş → _)
    .replace(/Do\s*rulama/g, "Doğrulama") // Fix "Doğrulama" with spacing (missing ğ)
    .replace(/Dorulama/g, "Doğrulama") // Fix missing ğ
    .replace(/0_renc1/g, "İğrenci") // Fix İğrenci if it appears
    .replace(/Ö_renc1/g, "Öğrenci") // Fix Öğrenci with ş → _
    // Remove unwanted "Man" text that appears on the right side
    .replace(/\s*Manual\s*$/i, "") // Remove trailing "Manual"
    .replace(/\s*Man\s*$/i, "") // Remove trailing "Man"
    .replace(/\s*Man\.\.\.\s*$/i, "") // Remove trailing "Man..."
    .replace(/\s*Man$/i, "") // Remove trailing "Man" without space
    .replace(/Manual\s*$/i, "") // Remove "Manual" at end
    .replace(/Man\s*$/i, "") // Remove "Man" at end
    .replace(/\bMan\b/gi, "") // Remove standalone "Man" word
    .replace(/\bManual\b/gi, "") // Remove standalone "Manual" word
    // Ensure proper Turkish characters are preserved
    .replace(/ı/g, "ı") // Ensure proper Turkish ı
    .replace(/İ/g, "İ") // Ensure proper Turkish İ
    .replace(/ğ/g, "ğ") // Ensure proper Turkish ğ
    .replace(/Ğ/g, "Ğ") // Ensure proper Turkish Ğ
    .replace(/ş/g, "ş") // Ensure proper Turkish ş
    .replace(/Ş/g, "Ş") // Ensure proper Turkish Ş
    .replace(/ç/g, "ç") // Ensure proper Turkish ç
    .replace(/Ç/g, "Ç") // Ensure proper Turkish Ç
    .replace(/ö/g, "ö") // Ensure proper Turkish ö
    .replace(/Ö/g, "Ö") // Ensure proper Turkish Ö
    .replace(/ü/g, "ü") // Ensure proper Turkish ü
    .replace(/Ü/g, "Ü"); // Ensure proper Turkish Ü

  return fixedText;
};

// Proper Turkish uppercase conversion
const toTurkishUpperCase = (text: string): string => {
  return text
    .replace(/i/g, "İ") // Turkish i -> İ
    .replace(/ı/g, "I") // Turkish ı -> I
    .toUpperCase();
};

// Get status color for PDF
const getStatusColor = (status: string): [number, number, number] => {
  switch (status.toLowerCase()) {
    case "present":
      return [39, 174, 96]; // Green
    case "absent":
      return [231, 76, 60]; // Red
    case "late":
      return [241, 196, 15]; // Yellow
    case "excused":
      return [52, 152, 219]; // Blue
    default:
      return [149, 165, 166]; // Gray
  }
};

// Get verification method icon
const getVerificationIcon = (method: string): string => {
  switch (method.toLowerCase()) {
    case "biometric":
      return "🔐"; // Fingerprint
    case "pin":
      return "🔢"; // PIN numbers
    case "manual":
      return "👤"; // Manual verification
    default:
      return "📝"; // Default
  }
};

// Generate CSV content with better formatting
export const generateCSV = (
  records: AttendanceRecord[],
  includeHeaders = true,
  dateRange?: { from: Date; to: Date },
  t?: TranslationFunction
): string => {
  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    return format(date, "MMMM dd, yyyy");
  };

  // Add metadata section with date range if provided
  const rawMetadata = [
    dateRange?.from && dateRange?.to
      ? [
          `${
            t ? t("attendance.export.attendanceReport") : "ATTENDANCE REPORT"
          }: ${formatDateForDisplay(dateRange.from)} ${
            t ? t("attendance.export.to") : "to"
          } ${formatDateForDisplay(dateRange.to)}`,
        ]
      : [t ? t("attendance.export.attendanceReport") : "ATTENDANCE REPORT"],
    [
      t ? t("attendance.export.generatedOn") : "Generated on:",
      format(new Date(), "MMMM dd, yyyy 'at' h:mm a"),
    ],
    [
      t ? t("attendance.export.totalRecords") : "Total Records:",
      records.length.toString(),
    ],
    [""], // Empty line for spacing
  ];

  // Use metadata as-is for CSV export (no text processing needed)
  const metadata = rawMetadata;

  // Enhanced headers with better descriptions - reordered to match PDF
  const rawHeaders = [
    t ? t("attendance.export.headers.dateTime") : "Date & Time",
    t ? t("attendance.export.headers.studentName") : "Student Name",
    t ? t("attendance.export.headers.studentId") : "Student ID",
    t ? t("attendance.export.headers.room") : "Room",
    t ? t("attendance.export.headers.status") : "Status",
    t ? t("attendance.export.headers.verification") : "Verification",
  ];

  // Use headers as-is for CSV export (no text processing needed)
  const headers = rawHeaders;

  // Sort records by date first, then by student name
  const sortedRecords = [...records].sort((a, b) => {
    // First sort by date (newest first)
    const dateA = new Date(a.timestamp).getTime();
    const dateB = new Date(b.timestamp).getTime();
    const dateCompare = dateB - dateA;

    if (dateCompare !== 0) return dateCompare;

    // Then sort by student name
    const nameA = a.studentName || "";
    const nameB = b.studentName || "";
    return nameA.localeCompare(nameB);
  });

  // Format all records - no grouping to show all attendance records
  const rows = sortedRecords.map((record) => {
    // Format the date in a more readable way for Excel
    const recordDate = new Date(record.timestamp);
    const locale = t && t("common.languageCode") === "tr" ? tr : enUS;
    const formattedDate = format(recordDate, "yyyy-MM-dd HH:mm", { locale }); // Format that Excel can recognize

    // Add status indicator icon
    let statusWithIcon =
      record.status?.toUpperCase() ||
      (t ? t("attendance.status.unknown") : "UNKNOWN");

    // Add color/icon indicators for status
    switch (record.status?.toLowerCase()) {
      case "present":
        statusWithIcon = `✅ ${
          t ? t("attendance.status.present").toUpperCase() : "PRESENT"
        }`;
        break;
      case "absent":
        statusWithIcon = `❌ ${
          t ? t("attendance.status.absent").toUpperCase() : "ABSENT"
        }`;
        break;
      case "late":
        statusWithIcon = `⏰ ${
          t ? t("attendance.status.late").toUpperCase() : "LATE"
        }`;
        break;
      case "excused":
        statusWithIcon = `📝 ${
          t ? toTurkishUpperCase(t("attendance.status.excused")) : "EXCUSED"
        }`;
        break;
    }

    // Simplify verification method and remove "Manual" suffix
    let verification =
      record.verificationMethod ||
      (t ? t("attendance.export.notAvailable") : "N/A");

    // Clean up verification text - remove "Manual" suffix and extra text
    if (
      verification.toLowerCase().includes("manual") ||
      verification.toLowerCase() === "manual"
    ) {
      verification = t
        ? t("attendance.export.manualByTeacher")
        : "Teacher Manual";
    }

    // Remove any trailing "Manual" text that might appear
    verification = verification.replace(/\s*Manual\s*$/i, "").trim();

    return [
      formattedDate,
      record.studentName ||
        (t ? t("attendance.export.unknownStudent") : "Unknown Student"),
      record.studentId || (t ? t("attendance.export.notAvailable") : "Unknown"),
      record.roomName ||
        record.roomId ||
        (t ? t("attendance.export.unknownRoom") : "Unknown Room"),
      statusWithIcon,
      verification,
    ];
  });

  // Calculate statistics by counting the actual formatted status text that will appear in CSV
  // This ensures the summary matches what's actually in the CSV content
  let presentCount = 0;
  let absentCount = 0;
  let lateCount = 0;
  let excusedCount = 0;

  records.forEach((record) => {
    // Use the same logic as the CSV row generation to determine status
    const status = record.status?.toLowerCase() || "unknown";

    switch (status) {
      case "present":
        presentCount++;
        break;
      case "absent":
        absentCount++;
        break;
      case "late":
        lateCount++;
        break;
      case "excused":
        excusedCount++;
        break;
    }
  });

  // Calculate percentages
  const totalCount = records.length;

  const presentPercentage =
    totalCount > 0 ? Math.round((presentCount / totalCount) * 100) : 0;
  const absentPercentage =
    totalCount > 0 ? Math.round((absentCount / totalCount) * 100) : 0;
  const latePercentage =
    totalCount > 0 ? Math.round((lateCount / totalCount) * 100) : 0;
  const excusedPercentage =
    totalCount > 0 ? Math.round((excusedCount / totalCount) * 100) : 0;

  // Add summary section with icons
  const summaryRows = [
    [""],
    [""], // Extra empty lines for spacing
    [t ? t("attendance.export.attendanceSummary") : "ATTENDANCE SUMMARY"],
    [""], // Empty line for spacing
    [
      `✅ ${t ? t("attendance.status.present") : "Present"}:`,
      `${presentCount} (${presentPercentage}%)`,
    ],
    [
      `❌ ${t ? t("attendance.status.absent") : "Absent"}:`,
      `${absentCount} (${absentPercentage}%)`,
    ],
    [
      `⏰ ${t ? t("attendance.status.late") : "Late"}:`,
      `${lateCount} (${latePercentage}%)`,
    ],
    [
      `📝 ${t ? t("attendance.status.excused") : "Excused"}:`,
      `${excusedCount} (${excusedPercentage}%)`,
    ],
    [""], // Empty line for spacing
    [
      t ? t("attendance.export.totalRecords") : "Total Records:",
      totalCount.toString(),
    ],
    [""],
    [""], // Extra empty lines for spacing
    [t ? t("attendance.export.reportInformation") : "REPORT INFORMATION"],
    [""], // Empty line for spacing
    [
      t ? t("attendance.export.generatedOn") : "Generated On:",
      format(new Date(), "MMMM dd, yyyy 'at' h:mm a"),
    ],
    dateRange?.from && dateRange?.to
      ? [
          t ? t("attendance.export.dateRange") : "Date Range:",
          `${formatDateForDisplay(dateRange.from)} ${
            t ? t("attendance.export.to") : "to"
          } ${formatDateForDisplay(dateRange.to)}`,
        ]
      : [
          t ? t("attendance.export.dateRange") : "Date Range:",
          t ? t("attendance.export.allDates") : "All dates",
        ],
    [""],
    [""], // Extra empty lines for spacing
    [
      t
        ? t("attendance.export.reportDescription")
        : "This report provides a summary of student attendance records for the selected date range.",
    ],
    [""], // Empty line for spacing
    [
      t
        ? `${getAppName(t)} - ${
            t("attendance.export.systemName").split(" - ")[1] ||
            t("attendance.export.systemName")
          }`
        : `${getAppName()} - Attendance Management System`,
    ],
  ];

  // Add 20px spacing between rows (by adding empty rows)
  const spacedRows = [];
  rows.forEach((row, index) => {
    spacedRows.push(row);
    // Add an empty row after each data row for spacing (except the last one)
    if (index < rows.length - 1) {
      spacedRows.push(["", "", "", "", "", ""]);
    }
  });

  // Combine all sections with extra spacing between sections
  const allRows = [
    ...metadata,
    [""],
    [""], // Extra empty lines for spacing
    ...(includeHeaders ? [headers] : []),
    [""], // Extra empty line after headers
    ...spacedRows,
    [""],
    [""],
    [""], // Extra empty lines for spacing
    ...summaryRows,
  ];

  // Properly escape CSV values to handle commas and quotes
  return allRows
    .map((row) =>
      row
        .map((cell) => {
          const cellStr = String(cell);
          // Escape commas and quotes in cell values
          return cellStr.includes(",") || cellStr.includes('"')
            ? `"${cellStr.replace(/"/g, '""')}"`
            : cellStr;
        })
        .join(",")
    )
    .join("\n");
};

// Download CSV file with Excel formatting
export const downloadCSV = (
  records: AttendanceRecord[],
  filename: string,
  dateRange?: { from: Date; to: Date },
  t?: TranslationFunction
) => {
  // Generate basic CSV content
  const csv = generateCSV(records, true, dateRange, t);

  // Create an Excel file with modern styling and formatting
  // This will create a properly formatted Excel file with modern colors and centered table

  // Get date range string for title with proper locale
  let dateRangeStr = t ? t("attendance.export.allDates") : "All Dates";
  if (dateRange?.from && dateRange?.to) {
    const locale = t && t("common.languageCode") === "tr" ? tr : enUS;
    const fromDate = format(dateRange.from, "MMM dd, yyyy", { locale });
    const toDate = format(dateRange.to, "MMM dd, yyyy", { locale });
    dateRangeStr = `${fromDate} ${
      t ? t("attendance.export.to") : "to"
    } ${toDate}`;
  }

  // Create an HTML file that Excel can open with proper formatting
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>${
    t ? t("attendance.export.attendanceReport") : "Attendance Report"
  }</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 95%;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
      border-radius: 8px;
    }
    h1 {
      color: #2c3e50;
      text-align: center;
      margin-bottom: 5px;
      font-size: 24px;
    }
    h2 {
      color: #7f8c8d;
      text-align: center;
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: normal;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      box-shadow: 0 2px 3px rgba(0,0,0,0.1);
    }
    th {
      background-color: #3498db;
      color: white;
      font-weight: bold;
      text-align: left;
      padding: 12px 20px;
      border: 1px solid #e0e0e0;
    }
    td {
      padding: 10px 20px;
      border: 1px solid #e0e0e0;
      vertical-align: middle;
    }
    tr:nth-child(even) {
      background-color: #f2f7ff;
    }
    tr:hover {
      background-color: #e8f4fc;
    }
    .present {
      color: #27ae60;
      font-weight: bold;
      background-color: #e8f5e9;
    }
    .absent {
      color: #e74c3c;
      font-weight: bold;
      background-color: #ffebee;
    }
    .late {
      color: #f39c12;
      font-weight: bold;
      background-color: #fff8e1;
    }
    .excused {
      color: #3498db;
      font-weight: bold;
      background-color: #e3f2fd;
    }
    .summary {
      margin-top: 30px;
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      border-left: 5px solid #3498db;
    }
    .summary h3 {
      margin-top: 0;
      color: #2c3e50;
    }
    .summary-item {
      margin: 10px 0;
      font-size: 14px;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      color: #7f8c8d;
      font-size: 12px;
    }
    .status-icon {
      margin-right: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>${
      t ? t("attendance.export.attendanceReport") : "ATTENDANCE REPORT"
    }</h1>
    <h2>${dateRangeStr}</h2>

    <table>
      <thead>
        <tr>
          <th>${
            t ? t("attendance.export.headers.dateTime") : "Date & Time"
          }</th>
          <th>${
            t ? t("attendance.export.headers.studentName") : "Student Name"
          }</th>
          <th>${
            t ? t("attendance.export.headers.studentId") : "Student ID"
          }</th>
          <th>${t ? t("attendance.export.headers.room") : "Room"}</th>
          <th>${t ? t("attendance.export.headers.status") : "Status"}</th>
          <th>${
            t ? t("attendance.export.headers.verification") : "Verification"
          }</th>
        </tr>
      </thead>
      <tbody>
        <!-- Data will be inserted here by JavaScript -->
      </tbody>
    </table>

    <div class="summary">
      <h3>${
        t ? t("attendance.export.attendanceSummary") : "Attendance Summary"
      }</h3>
      <div class="summary-item">✅ ${
        t ? t("attendance.status.present") : "Present"
      }: <span id="present-count">0</span></div>
      <div class="summary-item">❌ ${
        t ? t("attendance.status.absent") : "Absent"
      }: <span id="absent-count">0</span></div>
      <div class="summary-item">⏰ ${
        t ? t("attendance.status.late") : "Late"
      }: <span id="late-count">0</span></div>
      <div class="summary-item">📝 ${
        t ? t("attendance.status.excused") : "Excused"
      }: <span id="excused-count">0</span></div>
      <div class="summary-item">${
        t ? t("attendance.export.totalRecords") : "Total Records"
      }: <span id="total-count">0</span></div>
    </div>

    <div class="footer">
      ${t ? t("attendance.export.generatedOn") : "Generated on"} ${format(
    new Date(),
    "MMMM dd, yyyy 'at' h:mm a",
    { locale: t && t("common.languageCode") === "tr" ? tr : enUS }
  )} ${t ? t("attendance.export.by") : "by"} ${getAppName(t)}
    </div>
  </div>

  <script>
    // This script will run when Excel opens the HTML file
    document.addEventListener('DOMContentLoaded', function() {
      console.log('CSV processing script started');

      // The CSV data will be processed and inserted here
      const csvData = \`${csv}\`;

      console.log('CSV data length:', csvData.length);
      console.log('First 500 chars of CSV:', csvData.substring(0, 500));

      // Process the CSV data
      const lines = csvData.split('\\n');
      console.log('Total lines in CSV:', lines.length);

      let inDataSection = false;
      let presentCount = 0;
      let absentCount = 0;
      let lateCount = 0;
      let excusedCount = 0;
      let totalCount = 0;

      const tbody = document.querySelector('tbody');
      console.log('Found tbody element:', !!tbody);

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        console.log('Processing line', i, ':', line.substring(0, 100) + '...');

        // Skip metadata and headers - check for both English and Turkish headers
        if (line.includes('Date & Time,Student Name,Student ID') ||
            line.includes('Tarih ve Saat,Öğrenci Adı,Öğrenci No')) {
          console.log('Found header line, starting data section');
          inDataSection = true;
          continue;
        }

        if (inDataSection && line.includes(',')) {
          // This is a data row
          const cells = line.split(',');
          console.log('Data row cells:', cells);
          if (cells.length >= 6) {
            const date = cells[0];
            const name = cells[1];
            const id = cells[2];
            const room = cells[3];
            const status = cells[4];
            const verification = cells[5];

            // Skip empty rows (used for spacing)
            if (!date && !name && !id && !room && !status && !verification) {
              continue;
            }

            // Count statuses - check for both English and Turkish status names (case insensitive)
            // Remove ALL emojis and extra characters for better matching
            const statusUpper = status
              .replace(/[\u{1F300}-\u{1F9FF}]/gu, '') // Remove all emojis
              .replace(/[📝✅❌⏰🔍👤🔐🔢]/g, '') // Remove specific status emojis
              .trim()
              .toUpperCase();

            console.log('Processing status:', status, '-> cleaned:', statusUpper); // Debug log
            console.log('Status character codes:', Array.from(statusUpper).map(c => c + ' (' + c.charCodeAt(0) + ')').join(', '));

            if (statusUpper.includes('PRESENT') || statusUpper.includes('MEVCUT')) {
              presentCount++;
              totalCount++;
              console.log('Matched PRESENT/MEVCUT');
            } else if (statusUpper.includes('ABSENT') || statusUpper.includes('YOK')) {
              absentCount++;
              totalCount++;
              console.log('Matched ABSENT/YOK');
            } else if (statusUpper.includes('LATE') || statusUpper.includes('GEÇ')) {
              lateCount++;
              totalCount++;
              console.log('Matched LATE/GEÇ');
            } else if (statusUpper.includes('EXCUSED') || statusUpper.includes('İZİNLİ') || statusUpper.includes('IZINLI') || statusUpper.includes('İZINLI')) {
              excusedCount++;
              totalCount++;
              console.log('Matched EXCUSED/İZİNLİ/İZINLI');
            } else {
              console.log('Unmatched status:', statusUpper, 'Original:', status); // Debug log for unmatched statuses
              console.log('Testing specific matches:');
              console.log('  Contains EXCUSED:', statusUpper.includes('EXCUSED'));
              console.log('  Contains İZİNLİ:', statusUpper.includes('İZİNLİ'));
              console.log('  Contains IZINLI:', statusUpper.includes('IZINLI'));
            }

            // Determine status class - check for both English and Turkish status names (case insensitive)
            let statusClass = '';
            if (statusUpper.includes('PRESENT') || statusUpper.includes('MEVCUT')) statusClass = 'present';
            else if (statusUpper.includes('ABSENT') || statusUpper.includes('YOK')) statusClass = 'absent';
            else if (statusUpper.includes('LATE') || statusUpper.includes('GEÇ')) statusClass = 'late';
            else if (statusUpper.includes('EXCUSED') || statusUpper.includes('İZİNLİ') || statusUpper.includes('IZINLI') || statusUpper.includes('İZINLI')) statusClass = 'excused';

            // Create table row
            const tr = document.createElement('tr');
            tr.innerHTML = \`
              <td>\${date}</td>
              <td>\${name}</td>
              <td>\${id}</td>
              <td>\${room}</td>
              <td class="\${statusClass}">\${status}</td>
              <td>\${verification}</td>
            \`;
            tbody.appendChild(tr);
          }
        }

        // Stop processing when we reach the summary section - check for both English and Turkish
        if (line.includes('ATTENDANCE SUMMARY') || line.includes('YOKLAMA ÖZETİ')) {
          break;
        }
      }

      // Debug: Show final counts
      console.log('Final counts:', {
        present: presentCount,
        absent: absentCount,
        late: lateCount,
        excused: excusedCount,
        total: totalCount
      });

      // Update summary counts
      document.getElementById('present-count').textContent = presentCount + ' (' + Math.round((presentCount / totalCount) * 100) + '%)';
      document.getElementById('absent-count').textContent = absentCount + ' (' + Math.round((absentCount / totalCount) * 100) + '%)';
      document.getElementById('late-count').textContent = lateCount + ' (' + Math.round((lateCount / totalCount) * 100) + '%)';
      document.getElementById('excused-count').textContent = excusedCount + ' (' + Math.round((excusedCount / totalCount) * 100) + '%)';
      document.getElementById('total-count').textContent = totalCount;
    });
  </script>
</body>
</html>
  `;

  // Create a blob with the HTML content
  try {
    console.log(
      "Creating HTML export with content length:",
      htmlContent.length
    );
    console.log("CSV data embedded in HTML:", csv.substring(0, 200) + "...");

    // Create an HTML file that Excel can open
    const htmlBlob = new Blob([htmlContent], {
      type: "text/html;charset=utf-8",
    });
    const htmlFilename = filename.replace(".csv", ".html");

    console.log("HTML filename:", htmlFilename);

    // Create a download link
    const link = document.createElement("a");

    if ("msSaveBlob" in navigator) {
      (navigator as any).msSaveBlob(htmlBlob, htmlFilename);
    } else {
      link.href = URL.createObjectURL(htmlBlob);
      link.setAttribute("download", htmlFilename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    // Show a success message
    console.log("HTML export created successfully");
  } catch (error) {
    // If HTML approach fails, fall back to regular CSV
    console.warn("HTML formatting failed, falling back to CSV", error);
    const blob = new Blob(["\ufeff" + csv], {
      type: "text/csv;charset=utf-8;",
    }); // Add BOM for Excel
    const link = document.createElement("a");

    if ("msSaveBlob" in navigator) {
      (navigator as any).msSaveBlob(blob, filename);
    } else {
      link.href = URL.createObjectURL(blob);
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};

// Generate and download PDF with enhanced styling
export const downloadPDF = (
  records: AttendanceRecord[],
  title: string,
  filename: string,
  dateRange?: { from: Date; to: Date },
  t?: TranslationFunction
) => {
  // Use landscape orientation for better table fit
  const doc = new jsPDF({
    orientation: "landscape",
    unit: "mm",
    format: "a4",
  });

  // Set font to support Turkish characters better with proper encoding
  try {
    // Try to use a font that better supports Turkish characters
    doc.setFont("times", "normal");
  } catch (e) {
    // Fallback to helvetica if times is not available
    doc.setFont("helvetica", "normal");
  }

  doc.setCharSpace(0); // Reset character spacing to default

  // Add Turkish character support by setting proper encoding
  try {
    // Ensure proper UTF-8 encoding for Turkish characters
    doc.setLanguage("tr-TR");
  } catch (e) {
    console.warn("Language setting not supported, continuing with default");
  }

  // Set up colors for modern design
  const primaryColor = [44, 62, 80]; // Dark blue-gray
  const secondaryColor = [52, 152, 219]; // Bright blue
  const accentColor = [26, 188, 156]; // Teal
  const textColor = [52, 73, 94]; // Medium blue-gray
  const mutedColor = [127, 140, 141]; // Gray

  // Add header with background
  doc.setFillColor(...secondaryColor);
  doc.rect(0, 0, doc.internal.pageSize.width, 30, "F");

  // Add logo or school name
  doc.setFontSize(20); // Reduced font size to prevent overlapping
  doc.setTextColor(255, 255, 255); // White text on blue background
  doc.setFont(undefined, "bold");
  doc.text(fixTurkishText(getAppName(t)), 20, 20);

  // Add date range if provided
  if (dateRange?.from && dateRange?.to) {
    const locale = t && t("common.languageCode") === "tr" ? tr : enUS;
    const fromDate = format(dateRange.from, "MMM dd, yyyy", { locale });
    const toDate = format(dateRange.to, "MMM dd, yyyy", { locale });

    // Add subtitle with date range - split into two lines to prevent overlapping
    doc.setFontSize(12); // Reduced font size
    doc.setTextColor(255, 255, 255); // White
    doc.text(
      t ? t("attendance.export.attendanceReport") : "Attendance Report",
      doc.internal.pageSize.width - 20,
      15,
      { align: "right" }
    );
    doc.text(
      `${fromDate} ${t ? t("attendance.export.to") : "to"} ${toDate}`,
      doc.internal.pageSize.width - 20,
      25,
      { align: "right" }
    );
  } else {
    // Just add the title
    doc.setFontSize(14);
    doc.setTextColor(255, 255, 255); // White
    doc.text(title, doc.internal.pageSize.width - 20, 20, { align: "right" });
  }

  // Add metadata section with light background
  doc.setFillColor(240, 240, 240); // Light gray
  doc.rect(0, 30, doc.internal.pageSize.width, 15, "F");

  // Add metadata text
  doc.setFontSize(10);
  doc.setFont(undefined, "normal");
  doc.setTextColor(...textColor);

  const locale = t && t("common.languageCode") === "tr" ? tr : enUS;
  const generatedDate = formatTurkishCreationDate(new Date(), t);
  doc.text(
    fixTurkishText(
      `${
        t ? t("attendance.export.generatedOn") : "Generated on"
      }: ${generatedDate}`
    ),
    20,
    39
  );
  doc.text(
    `${t ? t("attendance.export.totalRecords") : "Total Records"}: ${
      records.length
    }`,
    doc.internal.pageSize.width - 20,
    39,
    { align: "right" }
  );

  // Calculate statistics
  const stats = records.reduce((acc, record) => {
    const status = record.status || "unknown";
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Ensure all status types are represented
  const allStatuses = ["present", "absent", "late", "excused"];
  allStatuses.forEach((status) => {
    if (stats[status] === undefined) {
      stats[status] = 0;
    }
  });

  // Add compact statistics boxes in a horizontal layout - wider to fill 95% of page
  const pageWidth = doc.internal.pageSize.width;
  const statsWidth = pageWidth * 0.95; // 95% of page width
  const statsBoxWidth = statsWidth / 4 - 10; // Divide by 4 statuses with some spacing
  const statsBoxHeight = 20; // Even smaller height
  const statsStartX = (pageWidth - statsWidth) / 2; // Center horizontally
  const statsStartY = 50;
  const statuses = ["present", "absent", "late", "excused"];

  // Add a title for the statistics section
  doc.setFontSize(10); // Smaller font
  doc.setFont(undefined, "bold");
  doc.setTextColor(...textColor);
  doc.text(
    t ? t("attendance.export.attendanceSummary") : "Attendance Summary",
    statsStartX,
    statsStartY - 3
  );

  // Draw a light background for the stats section
  doc.setFillColor(248, 249, 250); // Very light gray
  doc.roundedRect(
    statsStartX - 5,
    statsStartY - 8,
    statsWidth + 10, // Use the full stats width
    statsBoxHeight + 10, // Reduced height
    2,
    2,
    "F"
  );

  statuses.forEach((status, index) => {
    const count = stats[status] || 0;
    const percentage =
      records.length > 0 ? Math.round((count / records.length) * 100) : 0;
    const color = getStatusColor(status);
    const x = statsStartX + index * (statsBoxWidth + 5);

    // Draw card background - simplified with just a white box
    doc.setFillColor(255, 255, 255); // White
    doc.roundedRect(x, statsStartY, statsBoxWidth, statsBoxHeight, 2, 2, "F");

    // Draw status indicator as a colored line at the top
    doc.setFillColor(...color);
    doc.rect(x, statsStartY, statsBoxWidth, 3, "F");

    // Add count and percentage on the same line to save space
    doc.setFontSize(12); // Smaller font
    doc.setFont(undefined, "bold");
    doc.setTextColor(...textColor);
    doc.text(count.toString(), x + statsBoxWidth / 2, statsStartY + 15, {
      align: "center",
    });

    // Add status label
    doc.setFontSize(7); // Smaller font
    doc.setFont(undefined, "normal");
    doc.setTextColor(...mutedColor);
    const statusLabel = t
      ? t(`attendance.status.${status}`).toUpperCase()
      : status.toUpperCase();
    doc.text(statusLabel, x + statsBoxWidth / 2, statsStartY + 8, {
      align: "center",
    });

    // Add percentage
    doc.setFontSize(6); // Smaller font
    doc.text(`${percentage}%`, x + statsBoxWidth / 2, statsStartY + 20, {
      align: "center",
    });
  });

  // Add table with enhanced styling - reordered columns as requested
  const tableHeaders = [
    [
      fixTurkishText(
        t ? t("attendance.export.headers.dateTime") : "Date & Time"
      ),
      fixTurkishText(
        t ? t("attendance.export.headers.studentName") : "Student Name"
      ),
      fixTurkishText(
        t ? t("attendance.export.headers.studentId") : "Student ID"
      ),
      fixTurkishText(t ? t("attendance.export.headers.room") : "Room"),
      fixTurkishText(t ? t("attendance.export.headers.status") : "Status"),
      fixTurkishText(
        t ? t("attendance.export.headers.verification") : "Verification"
      ),
    ],
  ];

  // Sort records by date first, then by student name
  const sortedRecords = [...records].sort((a, b) => {
    // First sort by date (newest first)
    const dateA = new Date(a.timestamp).getTime();
    const dateB = new Date(b.timestamp).getTime();
    const dateCompare = dateB - dateA;

    if (dateCompare !== 0) return dateCompare;

    // Then sort by student name
    const nameA = a.studentName || "";
    const nameB = b.studentName || "";
    return nameA.localeCompare(nameB);
  });

  // Format all records - no grouping to show all attendance records
  const tableData = sortedRecords.map((record) => {
    // Format the date in a compact way
    const recordDate = new Date(record.timestamp);
    const locale = t && t("common.languageCode") === "tr" ? tr : enUS;
    const formattedDate = format(recordDate, "MMM dd, HH:mm", { locale });

    // Simplify verification method and remove "Manual" suffix
    let verification =
      record.verificationMethod ||
      (t ? t("attendance.export.notAvailable") : "N/A");

    // Clean up verification text - remove "Manual" suffix and extra text
    if (verification.toLowerCase().includes("manual")) {
      verification = t
        ? t("attendance.export.manualByTeacher")
        : "Teacher Manual";
    } else if (verification.toLowerCase() === "manual") {
      verification = t
        ? t("attendance.export.manualByTeacher")
        : "Teacher Manual";
    }

    // Remove any trailing "Manual" text that might appear
    verification = verification.replace(/\s*Manual\s*$/i, "").trim();

    // Translate status properly
    let translatedStatus = record.status || "unknown";
    if (t) {
      switch (translatedStatus.toLowerCase()) {
        case "present":
          translatedStatus = t("attendance.status.present");
          break;
        case "absent":
          translatedStatus = t("attendance.status.absent");
          break;
        case "late":
          translatedStatus = t("attendance.status.late");
          break;
        case "excused":
          translatedStatus = t("attendance.status.excused");
          break;
        default:
          translatedStatus = t("attendance.status.unknown");
      }
    }

    // Return data in the requested column order with Turkish text fixes
    return [
      fixTurkishText(formattedDate),
      fixTurkishText(
        record.studentName ||
          (t ? t("attendance.export.unknownStudent") : "Unknown Student")
      ),
      fixTurkishText(
        record.studentId ||
          (t ? t("attendance.export.notAvailable") : "Unknown")
      ),
      fixTurkishText(
        record.roomName ||
          record.roomId ||
          (t ? t("attendance.export.unknownRoom") : "Unknown Room")
      ),
      fixTurkishText(translatedStatus.toUpperCase()),
      fixTurkishText(verification),
    ];
  });

  // Title removed as requested

  autoTable(doc, {
    head: tableHeaders,
    body: tableData,
    startY: statsStartY + statsBoxHeight + 10, // Reduced spacing since title was removed
    styles: {
      fontSize: 8, // Slightly larger font for better readability
      cellPadding: 3, // Slightly more padding for better spacing
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
      overflow: "linebreak", // Enable text wrapping
      cellWidth: "wrap", // Allow cells to wrap text
    },
    headStyles: {
      fillColor: secondaryColor,
      textColor: 255,
      fontStyle: "bold",
      halign: "center",
      fontSize: 9, // Slightly larger header font
      cellPadding: 4, // More padding for headers
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
    },
    // Use 100% of available page width to prevent overflow
    tableWidth: pageWidth * 0.95,
    // Center the table horizontally with smaller margins
    margin: {
      left: (pageWidth - pageWidth * 0.95) / 2,
      right: (pageWidth - pageWidth * 0.95) / 2,
      top: 80,
      bottom: 20,
    },
    columnStyles: {
      0: { cellWidth: pageWidth * 0.15 }, // Date & Time
      1: { cellWidth: pageWidth * 0.2 }, // Student Name
      2: { cellWidth: pageWidth * 0.1 }, // Student ID - narrower
      3: { cellWidth: pageWidth * 0.15 }, // Room
      4: { cellWidth: pageWidth * 0.18, halign: "center" }, // Status - much wider for Turkish
      5: { cellWidth: pageWidth * 0.17, halign: "center" }, // Verification - wider for "Doğrulama"
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250], // Very light gray
    },
    // Prepare status data before drawing
    didParseCell: (data) => {
      // Fix Turkish text in all cells
      if (data.cell.text && typeof data.cell.text === "string") {
        data.cell.text = fixTurkishText(data.cell.text);
      }

      // For the status column
      if (data.section === "body" && data.column.index === 4) {
        // Get the status value
        const status = String(data.cell.raw || "").toLowerCase();

        // Set text colors based on status (support both English and Turkish)
        if (status.includes("present") || status.includes("mevcut")) {
          data.cell.styles.textColor = [39, 174, 96]; // Green text
        } else if (status.includes("absent") || status.includes("yok")) {
          data.cell.styles.textColor = [231, 76, 60]; // Red text
        } else if (status.includes("late") || status.includes("geç")) {
          data.cell.styles.textColor = [241, 196, 15]; // Yellow/orange text
        } else if (
          status.includes("excused") ||
          status.includes("izinli") ||
          status.includes("İzinli")
        ) {
          data.cell.styles.textColor = [52, 152, 219]; // Blue text
        }

        // Make status text bold and centered
        data.cell.styles.fontStyle = "bold";
        data.cell.styles.halign = "center";
      }

      // For header cells, ensure proper Turkish text
      if (data.section === "head") {
        if (data.cell.text && typeof data.cell.text === "string") {
          data.cell.text = fixTurkishText(data.cell.text);
        }
      }
    },

    // Add status icons when drawing cells
    willDrawCell: (data) => {
      // Add status icons for the status column
      if (data.section === "body" && data.column.index === 4) {
        try {
          // Get the status value
          const status = String(data.cell.text || "").toLowerCase();
          let icon = "";
          let color;

          // Set icon and color based on status (support both English and Turkish)
          if (status.includes("present") || status.includes("mevcut")) {
            icon = "✓"; // Checkmark
            color = [39, 174, 96]; // Green
          } else if (status.includes("absent") || status.includes("yok")) {
            icon = "✗"; // X mark
            color = [231, 76, 60]; // Red
          } else if (status.includes("late") || status.includes("geç")) {
            icon = "⏱"; // Clock
            color = [241, 196, 15]; // Yellow
          } else if (
            status.includes("excused") ||
            status.includes("izinli") ||
            status.includes("İzinli")
          ) {
            icon = "!"; // Exclamation
            color = [52, 152, 219]; // Blue
          }

          if (icon && color) {
            // Save current text color
            const currentTextColor = doc.getTextColor();

            // Draw the icon
            doc.setTextColor(...color);
            doc.setFontSize(10); // Slightly larger font for the icon
            doc.setFont(undefined, "bold");

            // Position the icon at the left of the cell
            const x = data.cell.x + 5;
            const y = data.cell.y + data.cell.height / 2 + 1;
            doc.text(icon, x, y);

            // Restore text settings
            doc.setTextColor(currentTextColor);
            doc.setFontSize(8);
            doc.setFont(undefined, "normal");
          }
        } catch (error) {
          console.warn("Error drawing status icon:", error);
        }
      }
    },
    // Set a smaller row height and ensure new pages start at the top
    rowPageBreak: "avoid",
    pageBreak: "auto",
    bodyStyles: {
      minCellHeight: 8, // Slightly larger minimum cell height for better readability
    },
    // Make sure new pages start at the top
    startY: function (data) {
      if (data.pageNumber > 1) {
        return 30; // Start at the top after the header on new pages
      }
      return statsStartY + statsBoxHeight + 10;
    },
  });

  // Add summary section at the bottom of the last page
  doc.setPage(doc.getNumberOfPages());

  // Get the final Y position after the table
  const finalY =
    (doc as any).lastAutoTable.finalY || doc.internal.pageSize.height - 30;

  // Add a summary section if there's enough space, otherwise add a new page
  if (finalY + 50 > doc.internal.pageSize.height) {
    doc.addPage();
    doc.setPage(doc.getNumberOfPages());
  }

  // Add summary title
  doc.setFontSize(12);
  doc.setFont(undefined, "bold");
  doc.setTextColor(...textColor);
  doc.text(
    t ? t("attendance.export.attendanceSummary") : "Attendance Summary",
    20,
    finalY + 15
  );

  // Add summary content
  doc.setFontSize(10);
  doc.setFont(undefined, "normal");
  doc.setTextColor(...mutedColor);

  let summaryY = finalY + 25;

  // Add summary text
  const presentCount = stats.present || 0;
  const absentCount = stats.absent || 0;
  const lateCount = stats.late || 0;
  const excusedCount = stats.excused || 0;
  const totalCount = records.length;

  const presentPercentage =
    totalCount > 0 ? Math.round((presentCount / totalCount) * 100) : 0;
  const absentPercentage =
    totalCount > 0 ? Math.round((absentCount / totalCount) * 100) : 0;
  const latePercentage =
    totalCount > 0 ? Math.round((lateCount / totalCount) * 100) : 0;
  const excusedPercentage =
    totalCount > 0 ? Math.round((excusedCount / totalCount) * 100) : 0;

  doc.text(
    `${
      t ? t("attendance.status.present") : "Present"
    }: ${presentCount} (${presentPercentage}%)`,
    20,
    summaryY
  );
  doc.text(
    `${
      t ? t("attendance.status.absent") : "Absent"
    }: ${absentCount} (${absentPercentage}%)`,
    80,
    summaryY
  );
  doc.text(
    `${
      t ? t("attendance.status.late") : "Late"
    }: ${lateCount} (${latePercentage}%)`,
    140,
    summaryY
  );
  doc.text(
    `${
      t ? t("attendance.status.excused") : "Excused"
    }: ${excusedCount} (${excusedPercentage}%)`,
    200,
    summaryY
  );

  // Add a note about the report
  doc.setFontSize(8);
  doc.text(
    t
      ? t("attendance.export.reportDescription")
      : "This report provides a summary of student attendance records for the selected date range.",
    20,
    summaryY + 15
  );

  // Add footer with page numbers and timestamp
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);

    // Add a footer background
    doc.setFillColor(240, 240, 240);
    doc.rect(
      0,
      doc.internal.pageSize.height - 15,
      doc.internal.pageSize.width,
      15,
      "F"
    );

    // Add page number
    doc.setFontSize(8);
    doc.setTextColor(...mutedColor);
    doc.text(
      `${t ? t("attendance.export.page") : "Page"} ${i} ${
        t ? t("attendance.export.of") : "of"
      } ${pageCount}`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 6,
      { align: "center" }
    );

    // Add timestamp on the left
    doc.text(
      `${t ? t("attendance.export.generated") : "Generated"}: ${format(
        new Date(),
        "yyyy-MM-dd HH:mm"
      )}`,
      15,
      doc.internal.pageSize.height - 6
    );

    // Add app name text on the right
    doc.text(
      getAppName(t),
      doc.internal.pageSize.width - 15,
      doc.internal.pageSize.height - 6,
      { align: "right" }
    );
  }

  // Save PDF
  doc.save(filename);
};

// Filter records by date range
export const filterRecordsByDateRange = (
  records: AttendanceRecord[],
  startDate: Date,
  endDate: Date
): AttendanceRecord[] => {
  // Set start date to beginning of day (00:00:00)
  const start = new Date(startDate);
  start.setHours(0, 0, 0, 0);

  // Set end date to end of day (23:59:59.999)
  const end = new Date(endDate);
  end.setHours(23, 59, 59, 999);

  console.log("Filtering records by date range:", {
    startDate: start.toISOString(),
    endDate: end.toISOString(),
    totalRecords: records.length,
  });

  const filteredRecords = records.filter((record) => {
    if (!record.timestamp) {
      console.log("Record missing timestamp:", record);
      return false;
    }

    const recordDate = new Date(record.timestamp);
    const isInRange = recordDate >= start && recordDate <= end;

    if (!isInRange) {
      console.log("Record outside date range:", {
        recordDate: recordDate.toISOString(),
        recordId: record.id,
        studentId: record.studentId,
      });
    }

    return isInRange;
  });

  console.log(
    `Filtered ${records.length} records to ${filteredRecords.length} records in date range`
  );
  return filteredRecords;
};

// Filter records by status
export const filterRecordsByStatus = (
  records: AttendanceRecord[],
  status: string
): AttendanceRecord[] => {
  console.log(`Filtering records by status: ${status}`);

  const filteredRecords = records.filter((record) => {
    // Case insensitive match
    return record.status?.toLowerCase() === status.toLowerCase();
  });

  console.log(
    `Filtered ${records.length} records to ${filteredRecords.length} records with status "${status}"`
  );
  return filteredRecords;
};

// Group records by date
export const groupRecordsByDate = (
  records: AttendanceRecord[]
): Record<string, AttendanceRecord[]> => {
  return records.reduce((groups, record) => {
    const date = format(new Date(record.timestamp), "yyyy-MM-dd");
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(record);
    return groups;
  }, {} as Record<string, AttendanceRecord[]>);
};

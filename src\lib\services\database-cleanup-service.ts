import { supabase } from "@/lib/supabase";
import { format, addDays, addWeeks, addMonths } from "date-fns";

/**
 * Interface for database cleanup settings
 */
export interface DatabaseCleanupSettings {
  id: string;
  enabled: boolean;
  notifications_retention_days: number;
  attendance_records_retention_days: number;
  audit_logs_retention_days: number;
  excuses_retention_days: number;
  alerts_retention_days: number;
  history_retention_days: number;
  user_activity_logs_retention_days: number;
  notification_logs_retention_days: number;
  selected_data_types: string[];
  last_cleanup_at: string | null;
  next_cleanup_at: string | null;
  cleanup_frequency: "daily" | "weekly" | "monthly";
  created_at: string;
  updated_at: string;
  created_by: string | null;
  updated_by: string | null;
}

/**
 * Interface for cleanup results
 */
export interface CleanupResults {
  notifications_deleted: number;
  attendance_records_deleted: number;
  audit_logs_deleted: number;
  excuses_deleted: number;
  alerts_deleted: number;
  history_deleted: number;
  user_activity_logs_deleted: number;
  notification_logs_deleted: number;
  next_cleanup: string;
  success: boolean;
}

/**
 * Get the current database cleanup settings
 * @returns The current database cleanup settings
 */
export const getDatabaseCleanupSettings =
  async (): Promise<DatabaseCleanupSettings | null> => {
    try {
      // First check if the table exists using a safer approach
      let tableExists = false;
      try {
        const { data, error } = await supabase
          .from("database_cleanup_settings")
          .select("id")
          .limit(1);

        if (!error) {
          tableExists = true;
        }
      } catch (e) {
        console.log("Error checking if table exists:", e);
        // Table doesn't exist
      }

      // If the table doesn't exist, return default settings
      if (!tableExists) {
        console.log("Database cleanup settings table may not exist yet");
        return getDefaultSettings();
      }

      // Try to get the settings
      const { data, error } = await supabase
        .from("database_cleanup_settings")
        .select("*")
        .limit(1)
        .single();

      if (error) {
        console.error("Error fetching database cleanup settings:", error);
        // If the table doesn't exist, return default settings
        if (error.code === "42P01") {
          return getDefaultSettings();
        }
        return getDefaultSettings();
      }

      return data;
    } catch (error) {
      console.error("Unexpected error in getDatabaseCleanupSettings:", error);
      return getDefaultSettings();
    }
  };

/**
 * Get default settings when the table doesn't exist yet
 */
const getDefaultSettings = (): DatabaseCleanupSettings => {
  return {
    id: "default",
    enabled: false,
    notifications_retention_days: 90,
    attendance_records_retention_days: 365,
    audit_logs_retention_days: 180,
    excuses_retention_days: 180,
    alerts_retention_days: 90,
    history_retention_days: 180,
    user_activity_logs_retention_days: 90,
    notification_logs_retention_days: 90,
    selected_data_types: [
      "notifications",
      "attendance_records",
      "audit_logs",
      "excuses",
      "alerts",
      "history",
      "user_activity_logs",
      "notification_logs",
    ],
    last_cleanup_at: null,
    next_cleanup_at: null,
    cleanup_frequency: "weekly",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    created_by: null,
    updated_by: null,
  };
};

/**
 * Update database cleanup settings
 * @param settings The settings to update
 * @returns True if the update was successful, false otherwise
 */
export const updateDatabaseCleanupSettings = async (
  settings: Partial<DatabaseCleanupSettings>,
  userId: string
): Promise<boolean> => {
  try {
    // First check if the table exists using a safer approach
    let tableExists = false;
    try {
      const { data, error } = await supabase
        .from("database_cleanup_settings")
        .select("id")
        .limit(1);

      if (!error) {
        tableExists = true;
      }
    } catch (e) {
      console.log("Error checking if table exists:", e);
      // Table doesn't exist
    }

    // If the table doesn't exist, run the migration to create it
    if (!tableExists) {
      console.log(
        "Database cleanup settings table may not exist yet, running migration..."
      );
      try {
        // Import dynamically to avoid circular dependencies
        const { runDatabaseCleanupMigration } = await import(
          "../migrations/run-database-cleanup-migration"
        );
        const migrationResult = await runDatabaseCleanupMigration();

        if (!migrationResult) {
          console.error("Failed to create database_cleanup_settings table");
          return false;
        }
      } catch (migrationError) {
        console.error(
          "Error running database cleanup migration:",
          migrationError
        );
        return false;
      }
    }

    // Calculate next cleanup date based on frequency
    let nextCleanupAt = null;
    if (settings.cleanup_frequency) {
      const now = new Date();
      if (settings.cleanup_frequency === "daily") {
        nextCleanupAt = addDays(now, 1).toISOString();
      } else if (settings.cleanup_frequency === "weekly") {
        nextCleanupAt = addWeeks(now, 1).toISOString();
      } else if (settings.cleanup_frequency === "monthly") {
        nextCleanupAt = addMonths(now, 1).toISOString();
      }
    }

    // If we have a default ID, we need to insert instead of update
    if (settings.id === "default") {
      // Create a new record
      const { error: insertError } = await supabase
        .from("database_cleanup_settings")
        .insert({
          ...getDefaultSettings(),
          ...settings,
          next_cleanup_at: nextCleanupAt || settings.next_cleanup_at,
          updated_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_by: userId,
          created_by: userId,
        });

      if (insertError) {
        console.error(
          "Error inserting database cleanup settings:",
          insertError
        );
        return false;
      }

      return true;
    }

    // Update the settings
    const { error } = await supabase
      .from("database_cleanup_settings")
      .update({
        ...settings,
        next_cleanup_at: nextCleanupAt || settings.next_cleanup_at,
        updated_at: new Date().toISOString(),
        updated_by: userId,
      })
      .eq("id", settings.id);

    if (error) {
      console.error("Error updating database cleanup settings:", error);

      // If the table doesn't exist or the record doesn't exist, try to insert
      if (error.code === "42P01" || error.message?.includes("not found")) {
        // Try to insert a new record
        const { error: insertError } = await supabase
          .from("database_cleanup_settings")
          .insert({
            ...getDefaultSettings(),
            ...settings,
            next_cleanup_at: nextCleanupAt || settings.next_cleanup_at,
            updated_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_by: userId,
            created_by: userId,
          });

        if (insertError) {
          console.error(
            "Error inserting database cleanup settings:",
            insertError
          );
          return false;
        }

        return true;
      }

      return false;
    }

    return true;
  } catch (error) {
    console.error("Unexpected error in updateDatabaseCleanupSettings:", error);
    return false;
  }
};

/**
 * Manually trigger a database cleanup
 * @param force If true, will clean up all records regardless of retention settings
 * @returns The results of the cleanup operation
 */
export const triggerDatabaseCleanup = async (
  force: boolean = true
): Promise<CleanupResults | null> => {
  try {
    // First check if the database_cleanup_settings table exists using a direct approach
    let tableExists = false;
    try {
      const { data, error } = await supabase
        .from("database_cleanup_settings")
        .select("id")
        .limit(1);

      if (!error) {
        tableExists = true;
      }
    } catch (e) {
      console.log("Error checking if table exists:", e);
      // Table doesn't exist
    }

    // If the table doesn't exist, run the migration
    if (!tableExists) {
      try {
        // Import dynamically to avoid circular dependencies
        const { runDatabaseCleanupMigration } = await import(
          "../migrations/run-database-cleanup-migration"
        );
        const migrationResult = await runDatabaseCleanupMigration();

        if (!migrationResult) {
          console.error("Failed to create database_cleanup_settings table");
          return getDefaultCleanupResults(false);
        }
      } catch (migrationError) {
        console.error(
          "Error running database cleanup migration:",
          migrationError
        );
        return getDefaultCleanupResults(false);
      }
    }

    // Check if the perform_database_cleanup function exists
    try {
      // Call the database function to perform the cleanup with force parameter
      const { data, error } = await supabase.rpc("perform_database_cleanup", {
        force: force,
      });

      if (error) {
        console.error("Error triggering database cleanup:", error);

        // If the function doesn't exist, run the migration
        if (
          error.message?.includes("function") &&
          error.message?.includes("does not exist")
        ) {
          try {
            // Import dynamically to avoid circular dependencies
            const { runDatabaseCleanupMigration } = await import(
              "../migrations/run-database-cleanup-migration"
            );
            await runDatabaseCleanupMigration();

            // Try again after migration
            const { data: retryData, error: retryError } = await supabase.rpc(
              "perform_database_cleanup",
              { force: force }
            );

            if (retryError) {
              console.error(
                "Error triggering database cleanup after migration:",
                retryError
              );
              return getDefaultCleanupResults(false);
            }

            // Success on retry
            return await getCleanupResults(retryData || false);
          } catch (migrationError) {
            console.error(
              "Error running database cleanup migration:",
              migrationError
            );
            return getDefaultCleanupResults(false);
          }
        }

        return getDefaultCleanupResults(false);
      }

      // Success on first try
      return await getCleanupResults(data || false);
    } catch (rpcError) {
      console.error("Exception in perform_database_cleanup RPC:", rpcError);
      return getDefaultCleanupResults(false);
    }
  } catch (error) {
    console.error("Unexpected error in triggerDatabaseCleanup:", error);
    return getDefaultCleanupResults(false);
  }
};

/**
 * Get cleanup results from audit log
 */
const getCleanupResults = async (success: boolean): Promise<CleanupResults> => {
  try {
    // Get the latest audit log entry for the cleanup
    const { data: auditLog, error: auditError } = await supabase
      .from("audit_logs")
      .select("details, created_at")
      .eq("action_type", "database_cleanup")
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (auditError || !auditLog) {
      console.error("Error fetching cleanup audit log:", auditError);
      return getDefaultCleanupResults(success);
    }

    return {
      notifications_deleted: auditLog.details?.notifications_deleted || 0,
      attendance_records_deleted:
        auditLog.details?.attendance_records_deleted || 0,
      audit_logs_deleted: auditLog.details?.audit_logs_deleted || 0,
      excuses_deleted: auditLog.details?.excuses_deleted || 0,
      alerts_deleted: auditLog.details?.alerts_deleted || 0,
      history_deleted: auditLog.details?.history_deleted || 0,
      user_activity_logs_deleted:
        auditLog.details?.user_activity_logs_deleted || 0,
      notification_logs_deleted:
        auditLog.details?.notification_logs_deleted || 0,
      next_cleanup: auditLog.details?.next_cleanup || new Date().toISOString(),
      success: success,
    };
  } catch (error) {
    console.error("Error in getCleanupResults:", error);
    return getDefaultCleanupResults(success);
  }
};

/**
 * Get default cleanup results
 */
const getDefaultCleanupResults = (success: boolean): CleanupResults => {
  return {
    notifications_deleted: 0,
    attendance_records_deleted: 0,
    audit_logs_deleted: 0,
    excuses_deleted: 0,
    alerts_deleted: 0,
    history_deleted: 0,
    user_activity_logs_deleted: 0,
    notification_logs_deleted: 0,
    next_cleanup: new Date().toISOString(),
    success: success,
  };
};

/**
 * Check if a database cleanup is due and trigger it if necessary
 * @param force If true, will clean up all records regardless of retention settings
 * @returns True if a cleanup was triggered, false otherwise
 */
export const checkAndTriggerCleanup = async (
  force: boolean = false
): Promise<boolean> => {
  try {
    // Get the current settings
    const settings = await getDatabaseCleanupSettings();

    // If settings don't exist or cleanup is disabled and not forced, don't trigger cleanup
    if (!settings || (!settings.enabled && !force)) {
      return false;
    }

    // If this is a default settings object (table doesn't exist yet), don't trigger cleanup
    if (settings.id === "default" && !force) {
      console.log(
        "Database cleanup settings table doesn't exist yet, skipping cleanup check"
      );
      return false;
    }

    // Check if a cleanup is due or forced
    const now = new Date();
    const nextCleanup = settings.next_cleanup_at
      ? new Date(settings.next_cleanup_at)
      : null;

    if (force || !nextCleanup || now >= nextCleanup) {
      console.log(
        force
          ? "Forced database cleanup triggered..."
          : "Database cleanup is due, triggering cleanup..."
      );

      // Trigger a cleanup with force parameter
      const result = await triggerDatabaseCleanup(force);

      if (result?.success) {
        console.log("Database cleanup completed successfully");
      } else {
        console.log("Database cleanup failed or was skipped");
      }

      return result?.success || false;
    }

    console.log(
      "Database cleanup is not due yet, next cleanup scheduled for:",
      nextCleanup
    );
    return false;
  } catch (error) {
    console.error("Unexpected error in checkAndTriggerCleanup:", error);
    return false;
  }
};

/**
 * Get cleanup statistics
 * @returns Statistics about the database and cleanup
 */
export const getCleanupStatistics = async (): Promise<any> => {
  try {
    // Handle all table queries with error handling
    const safeQuery = async (tableName: string) => {
      try {
        const { count, error } = await supabase
          .from(tableName)
          .select("id", { count: "exact", head: true });

        if (error) {
          console.log(`Error counting ${tableName}:`, error);
          return 0;
        }

        return count || 0;
      } catch (err) {
        console.log(`Exception counting ${tableName}:`, err);
        return 0;
      }
    };

    // Get counts from various tables with safe error handling
    const [
      notificationsCount,
      attendanceRecordsCount,
      auditLogsCount,
      excusesCount,
    ] = await Promise.all([
      safeQuery("notifications"),
      safeQuery("attendance_records"),
      safeQuery("audit_logs"),
      safeQuery("excuses"),
    ]);

    // For tables that might not exist, we'll handle them separately
    const alertsCount = await safeQuery("attendance_alerts");
    const historyCount = await safeQuery("history");
    const userActivityLogsCount = await safeQuery("user_activity_logs");
    const notificationLogsCount = await safeQuery("notification_logs");

    // Get the last cleanup details with error handling
    let lastCleanup = null;
    try {
      const { data, error } = await supabase
        .from("audit_logs")
        .select("details, created_at")
        .eq("action_type", "database_cleanup")
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (!error && data) {
        lastCleanup = {
          timestamp: data.created_at,
          details: data.details,
        };
      }
    } catch (cleanupError) {
      console.log("Error fetching last cleanup:", cleanupError);
      // Continue without cleanup data
    }

    return {
      current_counts: {
        notifications: notificationsCount,
        attendance_records: attendanceRecordsCount,
        audit_logs: auditLogsCount,
        excuses: excusesCount,
        alerts: alertsCount,
        history: historyCount,
        user_activity_logs: userActivityLogsCount,
        notification_logs: notificationLogsCount,
      },
      last_cleanup: lastCleanup,
    };
  } catch (error) {
    console.error("Unexpected error in getCleanupStatistics:", error);
    // Return default statistics instead of null
    return {
      current_counts: {
        notifications: 0,
        attendance_records: 0,
        audit_logs: 0,
        excuses: 0,
        alerts: 0,
        history: 0,
        user_activity_logs: 0,
        notification_logs: 0,
      },
      last_cleanup: null,
    };
  }
};

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2, ShieldAlert, ShieldCheck } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";

export default function TeacherPermissionsSettings() {
  const [canTeachersEdit, setCanTeachersEdit] = useState<boolean>(true);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();
  const { t } = useTranslation();

  // Fetch current permission settings
  const fetchPermissions = async () => {
    setLoading(true);
    try {
      // Get the latest permission record
      const { data, error } = await supabase
        .from("teacher_location_permissions")
        .select("*")
        .order("updated_at", { ascending: false })
        .limit(1);

      if (error) {
        console.error("Error fetching teacher permissions:", error);
        toast({
          title: t("common.error"),
          description: t("admin.settings.teacherPermissionsLoadError"),
          variant: "destructive",
        });
        // Default to true if there's an error
        setCanTeachersEdit(true);
      } else if (data && data.length > 0) {
        console.log("Teacher permissions:", data[0]);
        setCanTeachersEdit(data[0].can_edit_settings);
      } else {
        // No records found, default to true
        console.log("No teacher permissions found, using default (true)");
        setCanTeachersEdit(true);
      }
    } catch (error) {
      console.error("Error in fetchPermissions:", error);
      // Default to true if there's an error
      setCanTeachersEdit(true);
    } finally {
      setLoading(false);
    }
  };

  // Save permission settings
  const savePermissions = async () => {
    if (!profile) return;

    setSaving(true);
    try {
      // Insert a new record with the updated permission
      const { error } = await supabase
        .from("teacher_location_permissions")
        .insert({
          can_edit_settings: canTeachersEdit,
          updated_by: profile.id,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error("Error saving teacher permissions:", error);
        throw error;
      }

      toast({
        title: t("common.success"),
        description: t("admin.settings.teacherPermissionsUpdated", {
          canEdit: t(
            canTeachersEdit ? "admin.settings.can" : "admin.settings.cannot"
          ),
        }),
        variant: "default",
      });
    } catch (error) {
      console.error("Error saving teacher permissions:", error);
      toast({
        title: t("common.error"),
        description: t("admin.settings.teacherPermissionsSaveError"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Set up real-time subscription to permission changes
  useEffect(() => {
    // Subscribe to changes in the teacher_location_permissions table
    const channel = supabase
      .channel("admin-teacher-permissions")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "teacher_location_permissions",
        },
        (payload) => {
          console.log("Teacher permissions changed:", payload);
          // Refresh permissions when changes are detected
          fetchPermissions();
        }
      )
      .subscribe();

    // Clean up subscription when component unmounts
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Fetch permissions when component mounts
  useEffect(() => {
    fetchPermissions();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("admin.settings.teacherPermissions")}</CardTitle>
          <CardDescription>
            {t("admin.settings.controlTeacherPermissions")}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-6">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.settings.teacherPermissions")}</CardTitle>
        <CardDescription>
          {t("admin.settings.controlTeacherPermissions")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            {canTeachersEdit ? (
              <ShieldCheck className="h-5 w-5 text-green-500" />
            ) : (
              <ShieldAlert className="h-5 w-5 text-amber-500" />
            )}
            <Label htmlFor="teacher-edit-permission" className="text-base">
              {t("admin.settings.allowTeachersToEdit")}
            </Label>
          </div>
          <Switch
            id="teacher-edit-permission"
            checked={canTeachersEdit}
            onCheckedChange={setCanTeachersEdit}
            disabled={saving}
          />
        </div>

        <Alert
          variant={canTeachersEdit ? "default" : "warning"}
          className="mt-4"
        >
          <AlertDescription>
            {canTeachersEdit
              ? t("admin.settings.teachersCanEdit")
              : t("admin.settings.teachersCannotEdit")}
          </AlertDescription>
        </Alert>

        <Button
          onClick={savePermissions}
          disabled={saving}
          className="w-full mt-4"
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("admin.settings.saving")}
            </>
          ) : (
            t("admin.settings.savePermissions")
          )}
        </Button>
      </CardContent>
      <CardFooter className="space-y-4">
        <p className="text-sm text-muted-foreground">
          {canTeachersEdit
            ? t("admin.settings.teachersWillSeeAndModify")
            : t("admin.settings.teachersWillSeeMessage")}
        </p>

        {!canTeachersEdit && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-800 text-sm">
              {t("admin.settings.teacherPermissionsNotAllowed")}
            </p>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}

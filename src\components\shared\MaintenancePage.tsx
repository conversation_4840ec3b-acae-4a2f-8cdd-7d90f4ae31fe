import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Loader2, WrenchIcon, Al<PERSON><PERSON>riangle, RefreshCw } from "lucide-react";

interface MaintenancePageProps {
  schoolName?: string;
  message?: string;
  estimatedTime?: string;
}

export default function MaintenancePage({
  schoolName = "your school",
  message = "We're currently performing some maintenance to improve your experience.",
  estimatedTime = "a few hours",
}: MaintenancePageProps) {
  useEffect(() => {
    // Set document title
    document.title = `Maintenance Mode | Campus Guardian`;
  }, []);

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-indigo-100 flex flex-col items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-none shadow-xl">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center space-y-6">
              <div className="relative">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                  className="text-blue-500 absolute -top-1 -left-1"
                >
                  <WrenchIcon size={28} />
                </motion.div>
                <div className="h-24 w-24 bg-blue-100 rounded-full flex items-center justify-center">
                  <WrenchIcon size={40} className="text-blue-500" />
                </div>
              </div>

              <div className="space-y-2">
                <h1 className="text-2xl font-bold text-gray-900">
                  Maintenance in Progress
                </h1>
                <p className="text-gray-600">
                  {schoolName} is currently undergoing scheduled maintenance.
                </p>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 w-full">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-sm text-gray-700">{message}</p>
                    <p className="text-sm text-gray-500">
                      We expect to be back in {estimatedTime}.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4 w-full">
                <div className="flex items-center justify-center gap-2">
                  <div className="h-2 w-2 bg-blue-400 rounded-full animate-pulse" />
                  <div className="h-2 w-2 bg-blue-400 rounded-full animate-pulse delay-150" />
                  <div className="h-2 w-2 bg-blue-400 rounded-full animate-pulse delay-300" />
                </div>

                <Button
                  onClick={handleRefresh}
                  variant="outline"
                  className="w-full"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Check Again
                </Button>
              </div>

              <div className="text-xs text-gray-500">
                If you need immediate assistance, please contact your system
                administrator.
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

import { useState, useEffect } from "react";
import { useExcuses } from "@/hooks/useExcuses";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { format } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import { calculateDaysBetween, formatDuration } from "@/lib/date-utils";
import { useToast } from "@/hooks/use-toast";
import { useExcuseSettings } from "@/hooks/useExcuseSettings";
import { useTranslation } from "react-i18next";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  CalendarIcon,
  Clock,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Trash2,
  Info,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";

// Helper function to format date to YYYY-MM-DD without timezone conversion
const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Define the form schema
const getExcuseFormSchema = (settings: any, t: any) => {
  const baseSchema = z.object({
    room_id: z.string({
      required_error: t("excuses.pleaseSelectRoom"),
    }),
    start_date: z.date({
      required_error: t("excuses.pleaseSelectStartDate"),
    }),
    end_date: z.date({
      required_error: t("excuses.pleaseSelectEndDate"),
    }),
    start_time: z.string({
      required_error: t("excuses.pleaseEnterStartTime"),
    }),
    end_time: z.string({
      required_error: t("excuses.pleaseEnterEndTime"),
    }),
    reason: z
      .string({
        required_error: t("excuses.pleaseProvideReason"),
      })
      .min(10, {
        message: t("excuses.reasonMinLength"),
      })
      .max(500, {
        message: t("excuses.reasonMaxLength"),
      }),
  });

  // Add refinements
  return baseSchema
    .refine(
      (data) => {
        // Ensure end_date is not before start_date
        return new Date(data.end_date) >= new Date(data.start_date);
      },
      {
        message: t("excuses.endDateBeforeStartDate"),
        path: ["end_date"],
      }
    )
    .refine(
      (data) => {
        // If settings exist, check max days in advance
        if (settings) {
          const startDate = new Date(data.start_date);
          const today = new Date();
          const daysDifference = Math.floor(
            (startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
          );
          return daysDifference <= settings.max_days_in_advance;
        }
        return true;
      },
      {
        message: (settings) =>
          `Excuse requests can only be submitted up to ${
            settings?.max_days_in_advance || 30
          } days in advance`,
        path: ["start_date"],
      }
    )
    .refine(
      (data) => {
        // If settings exist, check max excuse duration
        if (settings) {
          const duration = calculateDaysBetween(data.start_date, data.end_date);
          return duration <= settings.max_excuse_duration_days;
        }
        return true;
      },
      {
        message: (settings) =>
          `Excuse duration cannot exceed ${
            settings?.max_excuse_duration_days || 14
          } days`,
        path: ["end_date"],
      }
    );
};

// Create a default schema for type inference
const excuseFormSchema = getExcuseFormSchema(null, (key: string) => key);
type ExcuseFormValues = z.infer<typeof excuseFormSchema>;

export default function Excuses() {
  const { profile } = useAuth();
  const { toast } = useToast();
  const { t, i18n } = useTranslation();

  // Get the appropriate locale for date formatting
  const getDateLocale = () => {
    return i18n.language === 'tr' ? tr : enUS;
  };
  const [rooms, setRooms] = useState<{ id: string; name: string }[]>([]);
  const [loadingRooms, setLoadingRooms] = useState(true);
  const [activeTab, setActiveTab] = useState("new");
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  // Fetch excuses using our custom hook
  const { excuses, loading, createExcuse, deleteExcuse } = useExcuses({
    role: "student",
  });

  // Get excuse settings
  const {
    settings,
    loading: loadingSettings,
    isWithinSubmissionHours,
  } = useExcuseSettings();

  // Initialize the form with dynamic schema based on settings
  const form = useForm<ExcuseFormValues>({
    resolver: zodResolver(getExcuseFormSchema(settings || null, t)),
    defaultValues: {
      reason: "",
      start_time: "08:00",
      end_time: "16:00",
    },
  });

  // Update form validation when settings change
  useEffect(() => {
    if (settings) {
      form.clearErrors();
      form.setError = form.setError;
    }
  }, [settings, form]);

  // Fetch rooms for the dropdown
  useEffect(() => {
    const fetchRooms = async () => {
      setLoadingRooms(true);
      try {
        // Get the student's block_id
        const { data: studentData, error: studentError } = await supabase
          .from("profiles")
          .select("block_id, room_id")
          .eq("id", profile?.id)
          .single();

        if (studentError) throw studentError;

        // Get rooms in the student's block
        let query = supabase.from("rooms").select("id, name");

        // Only filter by block_id if it's not null
        if (studentData.block_id) {
          query = query.eq("block_id", studentData.block_id);
        }

        const { data: roomsData, error: roomsError } = await query;

        if (roomsError) throw roomsError;

        setRooms(roomsData);

        // Set default room to student's assigned room
        if (studentData.room_id) {
          form.setValue("room_id", studentData.room_id);
        }
      } catch (error) {
        console.error("Error fetching rooms:", error);
        toast({
          title: t("excuses.errorFetchingRooms"),
          description: t("excuses.failedToLoadRooms"),
          variant: "destructive",
        });
      } finally {
        setLoadingRooms(false);
      }
    };

    fetchRooms();
  }, [profile]);

  // Handle form submission
  const onSubmit = async (values: ExcuseFormValues) => {
    try {
      // Check if submission is allowed based on time restrictions
      if (settings && !isWithinSubmissionHours()) {
        const currentTime = new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        });

        toast({
          title: t("excuses.submissionNotAvailable"),
          description: t("excuses.submissionTimeRestriction", {
            startTime: settings.submission_start_time.substring(0, 5),
            endTime: settings.submission_end_time.substring(0, 5),
            currentTime: currentTime,
          }),
          variant: "destructive",
        });
        return;
      }

      // Check max days in advance if settings exist
      if (settings) {
        const startDate = new Date(values.start_date);
        const today = new Date();
        const daysDifference = Math.floor(
          (startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysDifference > settings.max_days_in_advance) {
          toast({
            title: t("excuses.dateTooFarInAdvance"),
            description: t("excuses.maxDaysInAdvance", {
              days: settings.max_days_in_advance,
            }),
            variant: "destructive",
          });
          return;
        }

        // Check max excuse duration
        const excuseDuration = calculateDaysBetween(
          values.start_date,
          values.end_date
        );
        if (excuseDuration > settings.max_excuse_duration_days) {
          toast({
            title: t("excuses.durationTooLong"),
            description: t("excuses.maxExcuseDuration", {
              days: settings.max_excuse_duration_days,
            }),
            variant: "destructive",
          });
          return;
        }
      }

      await createExcuse({
        student_id: profile?.id || "",
        room_id: values.room_id,
        start_date: formatDateToYYYYMMDD(values.start_date),
        end_date: formatDateToYYYYMMDD(values.end_date),
        start_time: values.start_time,
        end_time: values.end_time,
        reason: values.reason,
      });

      // Reset form fields but keep the room selection
      const currentRoomId = form.getValues("room_id");
      form.reset({
        room_id: currentRoomId,
        start_time: "08:00",
        end_time: "16:00",
      });

      // Show the pending tab to see the newly submitted excuse
      setActiveTab("pending");
    } catch (error: any) {
      console.error("Error submitting excuse:", error);
      toast({
        title: t("common.error"),
        description: error.message || t("excuses.failedToSubmit"),
        variant: "destructive",
      });
    }
  };

  // Handle excuse deletion
  const handleDelete = async (excuseId: string) => {
    try {
      await deleteExcuse(excuseId);
      setConfirmDelete(null);
    } catch (error) {
      console.error("Error deleting excuse:", error);
    }
  };

  // Helper function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"
          >
            <Clock className="w-3 h-3" />
            <span>{t("excuses.pending")}</span>
          </Badge>
        );
      case "approved":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
          >
            <CheckCircle className="w-3 h-3" />
            <span>{t("excuses.approved")}</span>
          </Badge>
        );
      case "rejected":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"
          >
            <XCircle className="w-3 h-3" />
            <span>{t("excuses.rejected")}</span>
          </Badge>
        );
      default:
        return null;
    }
  };

  // Filter excuses by status
  const pendingExcuses = excuses.filter(
    (excuse) => excuse.status === "pending"
  );
  const approvedExcuses = excuses.filter(
    (excuse) => excuse.status === "approved"
  );
  const rejectedExcuses = excuses.filter(
    (excuse) => excuse.status === "rejected"
  );

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{t("excuses.attendanceExcuses")}</CardTitle>
        <CardDescription>
          {t("excuses.requestExcusesDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="new">{t("excuses.newRequest")}</TabsTrigger>
            <TabsTrigger value="pending" className="relative">
              {t("excuses.pending")}
              {pendingExcuses.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-[10px] text-primary-foreground w-4 h-4 rounded-full flex items-center justify-center font-medium">
                  {pendingExcuses.length}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="approved">{t("excuses.approved")}</TabsTrigger>
            <TabsTrigger value="rejected">{t("excuses.rejected")}</TabsTrigger>
          </TabsList>

          <TabsContent value="new" className="space-y-4">
            {settings && !isWithinSubmissionHours() ? (
              <div className="bg-amber-50 border border-amber-200 rounded-md p-4 text-amber-800">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 mt-0.5 text-amber-600" />
                  <div>
                    <p className="font-medium">
                      {t("excuses.submissionNotAvailable")}
                    </p>
                    <p className="mt-1">
                      {t("excuses.submissionTimeRestriction", {
                        startTime: settings.submission_start_time.substring(
                          0,
                          5
                        ),
                        endTime: settings.submission_end_time.substring(0, 5),
                        currentTime: new Date().toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        }),
                      })}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {pendingExcuses.length > 0 && (
                  <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-4 text-sm text-amber-800">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 mr-2 mt-0.5 text-amber-600" />
                      <div>
                        <p className="font-medium">
                          {t("excuses.pendingExcuseExists")}
                        </p>
                        <p>{t("excuses.pendingExcuseMessage")}</p>
                      </div>
                    </div>
                  </div>
                )}
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-4"
                  >
                    <FormField
                      control={form.control}
                      name="room_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("excuses.roomLabel")}</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={loadingRooms}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={t("excuses.selectRoom")} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {rooms.map((room) => (
                                <SelectItem key={room.id} value={room.id}>
                                  {t("excuses.room")} {room.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            {t("excuses.selectRoomDescription")}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="start_date"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>{t("excuses.startDate")}</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant={"outline"}
                                    className={cn(
                                      "w-full pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP", { locale: getDateLocale() })
                                    ) : (
                                      <span>{t("excuses.pickADate")}</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  initialFocus
                                  disabled={(date) => {
                                    const today = new Date(
                                      new Date().setHours(0, 0, 0, 0)
                                    );

                                    // Disable past dates
                                    if (date < today) return true;

                                    // Disable dates beyond max days in advance if settings exist
                                    if (
                                      settings &&
                                      settings.max_days_in_advance
                                    ) {
                                      const maxDate = new Date(today);
                                      maxDate.setDate(
                                        today.getDate() +
                                          settings.max_days_in_advance
                                      );
                                      return date > maxDate;
                                    }

                                    return false;
                                  }}
                                />
                              </PopoverContent>
                            </Popover>
                            <FormDescription>
                              {t("excuses.firstDayOfAbsence")}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="end_date"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>{t("excuses.endDate")}</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant={"outline"}
                                    className={cn(
                                      "w-full pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP", { locale: getDateLocale() })
                                    ) : (
                                      <span>{t("excuses.pickADate")}</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  initialFocus
                                  disabled={(date) => {
                                    const startDate =
                                      form.getValues("start_date");

                                    // Disable dates before start date
                                    if (startDate && date < startDate)
                                      return true;

                                    // Disable dates beyond max duration if settings exist
                                    if (
                                      settings &&
                                      settings.max_excuse_duration_days &&
                                      startDate
                                    ) {
                                      const maxEndDate = new Date(startDate);
                                      maxEndDate.setDate(
                                        maxEndDate.getDate() +
                                          settings.max_excuse_duration_days -
                                          1
                                      );
                                      return date > maxEndDate;
                                    }

                                    return false;
                                  }}
                                />
                              </PopoverContent>
                            </Popover>
                            <FormDescription>
                              {t("excuses.lastDayOfAbsence")}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Show duration calculation */}
                    {form.watch("start_date") && form.watch("end_date") && (
                      <div className="col-span-2 mt-2">
                        <div className="bg-muted p-2 rounded-md text-sm flex items-center">
                          <span className="text-primary font-medium mr-1">
                            Duration:
                          </span>
                          {formatDuration(
                            calculateDaysBetween(
                              form.watch("start_date"),
                              form.watch("end_date")
                            )
                          )}
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="start_time"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("excuses.startTime")}</FormLabel>
                            <FormControl>
                              <Input
                                type="time"
                                placeholder={t("excuses.startTimePlaceholder")}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="end_time"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("excuses.endTime")}</FormLabel>
                            <FormControl>
                              <Input
                                type="time"
                                placeholder={t("excuses.endTimePlaceholder")}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="reason"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("excuses.reason")}</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={t("excuses.reasonPlaceholder")}
                              className="resize-none min-h-[120px]"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t("excuses.reasonDescription")}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full"
                      disabled={pendingExcuses.length > 0}
                    >
                      {pendingExcuses.length > 0
                        ? t("excuses.deletePendingFirst")
                        : t("excuses.submitRequest")}
                    </Button>
                  </form>
                </Form>
              </div>
            )}
          </TabsContent>

          <TabsContent value="pending">
            {loading ? (
              <ExcusesSkeleton />
            ) : pendingExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <Clock className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("excuses.noPendingExcuses")}
                description={t("excuses.noPendingExcusesDescription")}
              />
            ) : (
              <div className="space-y-4">
                {pendingExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onDelete={() => setConfirmDelete(excuse.id)}
                    getStatusBadge={getStatusBadge}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="approved">
            {loading ? (
              <ExcusesSkeleton />
            ) : approvedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <CheckCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("excuses.noApprovedExcuses")}
                description={t("excuses.noApprovedExcusesDescription")}
              />
            ) : (
              <div className="space-y-4">
                {approvedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    getStatusBadge={getStatusBadge}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="rejected">
            {loading ? (
              <ExcusesSkeleton />
            ) : rejectedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <XCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("excuses.noRejectedExcuses")}
                description={t("excuses.noRejectedExcusesDescription")}
              />
            ) : (
              <div className="space-y-4">
                {rejectedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    getStatusBadge={getStatusBadge}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!confirmDelete}
        onOpenChange={() => setConfirmDelete(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("excuses.confirmDelete")}</DialogTitle>
            <DialogDescription>
              {t("excuses.confirmDeleteDescription")}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(null)}>
              {t("excuses.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={() => confirmDelete && handleDelete(confirmDelete)}
            >
              {t("excuses.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Helper components
interface ExcuseCardProps {
  excuse: any;
  onDelete?: () => void;
  getStatusBadge: (status: string) => JSX.Element | null;
}

function ExcuseCard({ excuse, onDelete, getStatusBadge }: ExcuseCardProps) {
  const { t } = useTranslation();

  return (
    <div className="border rounded-lg p-4 hover:border-primary/50 transition-colors">
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <h3 className="font-medium">
            {t("excuses.room")} {excuse.roomName || t("excuses.unknown")}
          </h3>
          <p className="text-sm text-muted-foreground">
            {format(new Date(excuse.start_date), "MMM d, yyyy", { locale: getDateLocale() })}
            {excuse.start_date !== excuse.end_date &&
              ` - ${format(new Date(excuse.end_date), "MMM d, yyyy", { locale: getDateLocale() })}`}
          </p>
          {excuse.start_date && excuse.end_date && (
            <p className="text-xs text-primary mt-1">
              {t("excuses.duration")}:{" "}
              {formatDuration(
                calculateDaysBetween(excuse.start_date, excuse.end_date)
              )}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(excuse.status)}
          {excuse.status === "pending" && onDelete && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-destructive"
              onClick={onDelete}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <Clock className="w-3.5 h-3.5" />
          <span>
            {excuse.start_time} - {excuse.end_time}
          </span>
        </div>
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <FileText className="w-3.5 h-3.5" />
          <span className="truncate">
            {excuse.reason.substring(0, 30)}
            {excuse.reason.length > 30 ? "..." : ""}
          </span>
        </div>
      </div>

      {(excuse.status === "approved" || excuse.status === "rejected") &&
        excuse.notes && (
          <div className="mt-3 p-2 bg-muted rounded text-sm">
            <p className="font-medium text-xs mb-1">
              {t("excuses.teacherNotes")}:
            </p>
            <p className="text-muted-foreground">{excuse.notes}</p>
          </div>
        )}
    </div>
  );
}

// This function is already defined in the main component

function EmptyState({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="text-center py-8">
      <div className="mx-auto mb-4">{icon}</div>
      <h3 className="text-lg font-medium mb-1">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
}

function ExcusesSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3].map((i) => (
        <div key={i} className="border rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <Skeleton className="h-5 w-[150px]" />
              <Skeleton className="h-4 w-[100px]" />
            </div>
            <Skeleton className="h-6 w-[80px]" />
          </div>
          <div className="mt-3 grid grid-cols-2 gap-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
        </div>
      ))}
    </div>
  );
}

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Room } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { QrCode, RefreshCw, Building2, Home } from "lucide-react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import QRCode from "react-qr-code";
import {
  generateSecureQRCode,
  getQRSecurityInfo,
  getQRExpirySeconds,
  getQRSecurityConfig,
} from "@/lib/services/qr-security-api";
import {
  websocketService,
  type AttendanceUpdate,
} from "@/lib/services/websocket-service";

interface Block {
  id: string;
  name: string;
  school_id: string;
}

export default function QRGenerator() {
  // State for blocks and rooms
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>("");
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);

  // QR code state
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [qrCodeData, setQrCodeData] = useState<string>("");
  const [qrSessionId, setQrSessionId] = useState<string>("");

  // Real-time attendance tracking
  const [recentAttendance, setRecentAttendance] = useState<AttendanceUpdate[]>(
    []
  );
  const [attendanceCount, setAttendanceCount] = useState<number>(0);

  const { toast } = useToast();
  const { t } = useTranslation();
  const { profile } = useAuth();

  // Fetch blocks and rooms on component mount
  useEffect(() => {
    const fetchBlocksAndRooms = async () => {
      try {
        setLoading(true);

        // Fetch blocks for the current school
        let blocksQuery = supabase.from("blocks").select("*").order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          blocksQuery = blocksQuery.eq("school_id", profile.school_id);
        }

        const { data: blocksData, error: blocksError } = await blocksQuery;

        if (blocksError) {
          console.error("Error fetching blocks:", blocksError);
          throw blocksError;
        }

        console.log("Fetched blocks for QR Generator:", blocksData);
        setBlocks(blocksData || []);

        // Fetch rooms for the current school
        let roomsQuery = supabase
          .from("rooms")
          .select(
            `
            id,
            name,
            building,
            floor,
            capacity,
            teacher_id,
            block_id,
            school_id,
            current_qr_code,
            qr_expiry,
            blocks (
              id,
              name
            )
          `
          )
          .order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: roomsData, error: roomsError } = await roomsQuery;

        if (roomsError) {
          console.error("Error fetching rooms:", roomsError);
          throw roomsError;
        }

        console.log("Fetched rooms for QR Generator:", roomsData);
        setRooms(roomsData || []);
      } catch (error) {
        console.error("Error fetching blocks and rooms:", error);
        toast({
          title: "Error",
          description: "Failed to load blocks and rooms",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchBlocksAndRooms();
  }, [profile?.school_id, profile?.access_level]);

  // Subscribe to real-time attendance updates for selected room
  useEffect(() => {
    if (!selectedRoom || !profile?.school_id) return;

    const unsubscribe = websocketService.subscribeToAttendance(
      profile.school_id,
      (update: AttendanceUpdate) => {
        if (update.data.room_id === selectedRoom.id) {
          // Add to recent attendance list
          setRecentAttendance((prev) => [update, ...prev.slice(0, 9)]); // Keep last 10
          setAttendanceCount((prev) => prev + 1);

          // Show toast notification
          toast({
            title: "New Attendance",
            description: `${update.data.student_name} checked in to ${update.data.room_name}`,
          });
        }
      },
      selectedRoom.id
    );

    return unsubscribe;
  }, [selectedRoom, profile?.school_id]);

  // QR code expiry timer with proactive regeneration
  useEffect(() => {
    if (qrExpiry) {
      const interval = setInterval(() => {
        const remaining = Math.max(
          0,
          Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
        );
        setTimeLeft(remaining);

        // Proactive regeneration: Generate new QR code 15 seconds before expiry
        // This ensures seamless transition without any waiting time for students
        if (remaining <= 15 && remaining > 0 && selectedRoom) {
          console.log(
            `Proactively regenerating QR code with ${remaining}s remaining`
          );
          clearInterval(interval);
          generateQRCode(selectedRoom);
        } else if (remaining <= 0) {
          // Fallback: Generate immediately if we somehow missed the proactive window
          clearInterval(interval);
          if (selectedRoom) {
            console.log("Emergency QR code regeneration - expired");
            generateQRCode(selectedRoom);
          }
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [qrExpiry, selectedRoom]);

  // Handle block selection
  const handleBlockChange = (blockId: string) => {
    setSelectedBlock(blockId);
    setSelectedRoom(null);
    setQrCodeData("");
    setQrExpiry(null);
    setTimeLeft(0);
  };

  // Handle room selection
  const handleRoomChange = (roomId: string) => {
    const room = rooms.find((r) => r.id === roomId);
    if (room) {
      setSelectedRoom(room);

      // Check if room has existing QR code that's still valid
      if (room.current_qr_code && room.qr_expiry) {
        const expiry = new Date(room.qr_expiry);
        if (expiry.getTime() > Date.now()) {
          // Use existing QR code
          setQrCodeData(room.current_qr_code);
          setQrExpiry(expiry);
          setTimeLeft(
            Math.max(0, Math.floor((expiry.getTime() - Date.now()) / 1000))
          );
          return;
        }
      }

      // Generate new QR code if none exists or expired
      generateQRCode(room);
    }
  };

  // Generate QR code for a room
  const generateQRCode = async (room: Room) => {
    try {
      // Get configurable expiry time
      const expirySeconds = getQRExpirySeconds();

      if (!profile?.id) {
        throw new Error("User ID is required for QR generation");
      }

      console.log(`Generating QR code on server for room ${room.id}`);

      // Generate cryptographically secure QR code on SERVER (SECURITY FIRST!)
      const secureQRData = await generateSecureQRCode(
        room.id,
        room.school_id || profile?.school_id || "",
        room.block_id,
        expirySeconds,
        profile.id
      );

      // Set expiry time
      const newExpiry = new Date(secureQRData.expires_at);

      // Use FULL SECURE format - no data truncation, maximum security
      const qrDataString = JSON.stringify(secureQRData);

      console.log(
        `Server generated QR code for room ${room.id} with session ${secureQRData.session_id}`
      );

      // Server has already updated the database, just set local state
      setQrSessionId(secureQRData.session_id);
      setQrExpiry(newExpiry);
      setTimeLeft(Math.floor((newExpiry.getTime() - Date.now()) / 1000));
      setQrCodeData(qrDataString);

      const expiryMinutes = Math.round(expirySeconds / 60);
      toast({
        title: "QR Code Generated",
        description: `New secure QR code for ${
          room.name
        } will expire in ${expiryMinutes} minute${
          expiryMinutes !== 1 ? "s" : ""
        }`,
      });

      console.log(
        `Server-side QR code generation completed for room ${room.id}`
      );
    } catch (error) {
      console.error("Error generating QR code:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to generate QR code",
        variant: "destructive",
      });
    }
  };

  const formatTimeLeft = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Get rooms for selected block
  const filteredRooms = selectedBlock
    ? rooms.filter((room) => room.block_id === selectedBlock)
    : [];

  if (loading) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>{t("admin.qrGenerator.title")}</CardTitle>
          <CardDescription>
            {t("admin.qrGenerator.description")}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <QrCode className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>{t("loading.blocksAndRooms")}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{t("admin.qrGenerator.title")}</CardTitle>
        <CardDescription>{t("admin.qrGenerator.description")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Block Selection */}
        <div className="space-y-2">
          <Label htmlFor="block" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            {t("admin.qrGenerator.selectBlock")}
          </Label>
          <Select value={selectedBlock} onValueChange={handleBlockChange}>
            <SelectTrigger>
              <SelectValue placeholder={t("admin.qrGenerator.chooseBlock")} />
            </SelectTrigger>
            <SelectContent>
              {blocks.map((block) => (
                <SelectItem key={block.id} value={block.id}>
                  {t("admin.qrGenerator.block")} {block.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Room Selection */}
        {selectedBlock && (
          <div className="space-y-2">
            <Label htmlFor="room" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              {t("admin.qrGenerator.selectRoom")}
            </Label>
            <Select
              value={selectedRoom?.id || ""}
              onValueChange={handleRoomChange}
            >
              <SelectTrigger>
                <SelectValue placeholder={t("admin.qrGenerator.chooseRoom")} />
              </SelectTrigger>
              <SelectContent>
                {filteredRooms.map((room) => (
                  <SelectItem key={room.id} value={room.id}>
                    {room.name} {room.building && `- ${room.building}`}{" "}
                    {room.floor && `${t("admin.qrGenerator.floor")} ${room.floor}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* QR Code Display */}
        {selectedRoom && (
          <div className="border rounded-md p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-3">
              <div className="flex-1 min-w-0">
                <h3 className="text-base sm:text-lg font-medium truncate">{selectedRoom.name}</h3>
                <p className="text-xs sm:text-sm text-muted-foreground">
                  {t("admin.qrGenerator.block")}{" "}
                  {blocks.find((b) => b.id === selectedRoom.block_id)?.name}
                  {selectedRoom.building && ` - ${selectedRoom.building}`}
                  {selectedRoom.floor && `, ${t("admin.qrGenerator.floor")} ${selectedRoom.floor}`}
                </p>
              </div>
              <div className="flex-shrink-0">
                <Button
                  onClick={() => generateQRCode(selectedRoom)}
                  className="w-full sm:w-auto flex items-center justify-center gap-1 text-sm"
                  size="sm"
                >
                  <RefreshCw size={16} className="flex-shrink-0" />
                  <span className="hidden sm:inline">{t("admin.qrGenerator.regenerateQRCode")}</span>
                  <span className="sm:hidden">Regenerate</span>
                </Button>
              </div>
            </div>

            <div className="flex flex-col lg:flex-row items-start space-y-6 lg:space-y-0 lg:space-x-6">
              <div className="flex flex-col items-center w-full lg:w-auto">
                {qrCodeData ? (
                  <div className="border-4 border-primary p-3 sm:p-4 rounded-md bg-white max-w-full">
                    <QRCode
                      value={qrCodeData}
                      size={window.innerWidth < 640 ? 180 : 200}
                      style={{
                        height: "auto",
                        maxWidth: "100%",
                        width: "100%",
                      }}
                    />
                  </div>
                ) : (
                  <div className="border-4 border-gray-200 p-3 sm:p-4 rounded-md w-48 h-48 sm:w-56 sm:h-56 flex items-center justify-center">
                    <p className="text-muted-foreground text-center text-sm">
                      No QR code generated
                    </p>
                  </div>
                )}

                <div className="mt-3 text-center">
                  {timeLeft > 0 ? (
                    <div className="flex flex-col items-center">
                      <span className="font-medium text-sm sm:text-base">
                        {t("admin.qrGenerator.expiresIn")}
                      </span>
                      <span
                        className={`text-lg sm:text-xl font-bold ${
                          timeLeft < 60
                            ? "text-red-600 animate-pulse"
                            : "text-primary"
                        }`}
                      >
                        {formatTimeLeft(timeLeft)}
                      </span>
                    </div>
                  ) : (
                    <span className="text-destructive font-medium text-sm sm:text-base">
                      Expired
                    </span>
                  )}
                </div>
              </div>

              <div className="flex-1 space-y-4 w-full lg:w-auto">
                <div>
                  <Label htmlFor="qr-data" className="text-sm font-medium">{t("admin.qrGenerator.qrCodeDataJSON")}</Label>
                  <textarea
                    id="qr-data"
                    value={
                      qrCodeData
                        ? JSON.stringify(JSON.parse(qrCodeData), null, 2)
                        : ""
                    }
                    readOnly
                    className="w-full p-2 border rounded-md font-mono text-xs h-24 sm:h-32 resize-none"
                  />
                </div>

                <div>
                  <Label htmlFor="room-capacity" className="text-sm font-medium">
                    {t("admin.qrGenerator.roomCapacity")}
                  </Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="room-capacity"
                      value={selectedRoom?.capacity || 0}
                      readOnly
                      className="text-sm"
                    />
                    <span className="text-sm text-muted-foreground whitespace-nowrap">
                      {t("admin.qrGenerator.students")}
                    </span>
                  </div>
                </div>

                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="font-medium text-blue-800 text-sm sm:text-base">
                    {t("admin.qrGenerator.qrCodeSecurityFeatures")}
                  </h4>
                  <ul className="text-xs sm:text-sm text-blue-700 list-disc list-inside mt-2 space-y-1">
                    <li>
                      {t("admin.qrGenerator.timeLimitedExpiration")} (
                      {Math.round(getQRExpirySeconds() / 60)} {t("admin.qrGenerator.minute")}
                      {Math.round(getQRExpirySeconds() / 60) !== 1 ? t("admin.qrGenerator.minutePlural") : ""})
                    </li>
                    <li>{t("admin.qrGenerator.fullUUIDPreservation")}</li>
                    <li>{t("admin.qrGenerator.completeCryptographicSignatures")}</li>
                    <li>{t("admin.qrGenerator.roomAndBlockValidation")}</li>
                    <li>{t("admin.qrGenerator.sessionBasedTracking")}</li>
                    <li>{t("admin.qrGenerator.timestampsToPrevent")}</li>
                  </ul>
                  <div className="mt-3 pt-2 border-t border-blue-300">
                    <p className="text-xs text-blue-600 font-mono break-all">
                      🔐 {t("admin.qrGenerator.qrChallengeRotates", "QR Challenge rotates every {{interval}}s, valid for {{total}}s total", {
                        interval: getQRSecurityConfig().challengeRotationInterval,
                        total: getQRSecurityConfig().effectiveValidityWindow
                      })}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      🛡️ {t("admin.qrGenerator.securityPriority")}
                    </p>
                  </div>
                </div>

                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <h4 className="font-medium text-green-800 text-sm sm:text-base">
                    {t("admin.qrGenerator.roomInformation")}
                  </h4>
                  <div className="text-xs sm:text-sm text-green-700 mt-2 space-y-1">
                    <p className="break-all">
                      <strong>{t("admin.qrGenerator.roomID")}:</strong> {selectedRoom.id}
                    </p>
                    <p>
                      <strong>{t("admin.qrGenerator.block")}:</strong>{" "}
                      {blocks.find((b) => b.id === selectedRoom.block_id)?.name}
                    </p>
                    <p>
                      <strong>{t("admin.qrGenerator.capacity")}:</strong> {selectedRoom.capacity}{" "}
                      {t("admin.qrGenerator.students")}
                    </p>
                    {qrSessionId && (
                      <p className="break-all">
                        <strong>{t("admin.qrGenerator.sessionID")}:</strong> {qrSessionId}
                      </p>
                    )}
                  </div>
                </div>

                {/* Real-time Attendance Tracking */}
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="font-medium text-blue-800 flex flex-col sm:flex-row sm:items-center gap-2 text-sm sm:text-base">
                    <span>📊 {t("admin.qrGenerator.liveAttendance")}</span>
                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full self-start sm:self-center">
                      {attendanceCount}
                    </span>
                  </h4>
                  {recentAttendance.length > 0 ? (
                    <div className="mt-2 space-y-1 max-h-24 sm:max-h-32 overflow-y-auto">
                      {recentAttendance.map((attendance, index) => (
                        <div
                          key={index}
                          className="text-xs text-blue-700 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-2"
                        >
                          <span className="truncate">{attendance.data.student_name}</span>
                          <span className="text-blue-500 text-xs">
                            {new Date(
                              attendance.data.timestamp
                            ).toLocaleTimeString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-xs text-blue-600 mt-2">
                      {t("admin.qrGenerator.noAttendanceRecorded")}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between text-sm text-muted-foreground">
        <div>
          {selectedRoom
            ? t("admin.qrGenerator.qrCodesExpireAfter", "QR codes automatically expire after {{minutes}} minutes for security", {
                minutes: Math.round(getQRExpirySeconds() / 60)
              })
            : t("admin.qrGenerator.selectBlockAndRoom")}
        </div>
      </CardFooter>
    </Card>
  );
}

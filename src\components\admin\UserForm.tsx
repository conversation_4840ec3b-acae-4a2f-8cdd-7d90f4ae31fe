import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { School } from "@/lib/types";
import { getAllSchools } from "@/lib/utils/school-context";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  role: z.enum(["student", "teacher", "admin"]),
  password: z.string().min(6, "Password must be at least 6 characters"),
  schoolId: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface UserFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

export default function UserForm({ onClose, onSuccess }: UserFormProps) {
  const [loading, setLoading] = useState(false);
  const [schools, setSchools] = useState<School[]>([]);
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool } = useSchool();
  const { t } = useTranslation();

  // Fetch schools on component mount
  useEffect(() => {
    const fetchSchools = async () => {
      try {
        const schoolsData = await getAllSchools();
        setSchools(schoolsData.filter((school) => school.isActive !== false));
      } catch (error) {
        console.error("Error fetching schools:", error);
        toast({
          title: "Error",
          description: "Failed to fetch schools",
          variant: "destructive",
        });
      }
    };

    fetchSchools();
  }, []);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      role: "student",
      password: "",
      schoolId: currentSchool?.id || "",
    },
  });

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      // Set a flag to prevent auto-redirect during user creation
      window.localStorage.setItem("admin_creating_user", "true");

      // 1. Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name,
            role: data.role,
          },
        },
      });

      if (authError) throw authError;

      if (!authData.user) {
        throw new Error("User creation failed");
      }

      // 2. Create profile
      const profileData = {
        id: authData.user.id,
        user_id: authData.user.id,
        name: data.name,
        email: data.email,
        role: data.role,
        created_at: new Date().toISOString(),
        // Add role-specific IDs
        student_id: data.role === "student" ? `S-${Date.now()}` : null,
        teacher_id: data.role === "teacher" ? `T-${Date.now()}` : null,
        admin_id: data.role === "admin" ? `A-${Date.now()}` : null,
        // Add school ID
        school_id:
          data.schoolId || currentSchool?.id || profile?.school_id || null,
        // Set access level for admin users (default to school admin)
        access_level: data.role === "admin" ? 1 : null,
        // New users need to complete their profile setup
        profile_completed: false,
        is_blocked: false,
      };

      // Use upsert to handle potential duplicate key conflicts
      const { error: profileError } = await supabase
        .from("profiles")
        .upsert(profileData, { onConflict: "id" });

      if (profileError) throw profileError;

      // Clear the flag
      window.localStorage.removeItem("admin_creating_user");

      toast({
        title: "Success",
        description: `${data.name} has been created successfully as a ${data.role}. They can now log in with their credentials.`,
      });

      // Reset form
      form.reset();

      // Close the dialog
      onClose();

      // Call success callback to refresh the user list
      onSuccess();
    } catch (error: any) {
      console.error("Error creating user:", error);

      // Clear the flag in case of error
      window.localStorage.removeItem("admin_creating_user");

      let errorMessage = "Failed to create user. Please try again.";

      // Provide more specific error messages
      if (error.message?.includes("duplicate key")) {
        errorMessage =
          "A user with this email already exists. Please use a different email.";
      } else if (error.message?.includes("email")) {
        errorMessage = "Invalid email address or email already in use.";
      } else if (error.message?.includes("password")) {
        errorMessage = "Password must be at least 6 characters long.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("admin.userManagement.addNewUser")}</DialogTitle>
          <DialogDescription>
            {t("admin.userManagement.addNewUserDescription")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("admin.userManagement.fullName")}</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={loading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("admin.userManagement.email")}</FormLabel>
                  <FormControl>
                    <Input {...field} type="email" disabled={loading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("admin.userManagement.role")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={loading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={`${t("admin.userManagement.role")}...`}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="student">
                        {t("admin.userManagement.students").slice(0, -1)}
                      </SelectItem>
                      <SelectItem value="teacher">
                        {t("admin.userManagement.teachers").slice(0, -1)}
                      </SelectItem>
                      <SelectItem value="admin">
                        {t("admin.userManagement.administrators").slice(0, -1)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("admin.userManagement.temporaryPassword")}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} type="password" disabled={loading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {schools.length > 0 && (
              <FormField
                control={form.control}
                name="schoolId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("admin.userManagement.school")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={loading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={`${t(
                              "admin.userManagement.school"
                            )}...`}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {schools.map((school) => (
                          <SelectItem key={school.id} value={school.id}>
                            {school.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                {t("admin.userManagement.cancel")}
              </Button>
              <Button type="submit" disabled={loading}>
                {loading
                  ? t("admin.userManagement.creating")
                  : t("admin.userManagement.createUser")}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

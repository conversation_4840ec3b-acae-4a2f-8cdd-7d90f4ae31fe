/**
 * 🤖 Automatic QR Code Generation Service
 * 
 * This service automatically generates QR codes for all rooms during attendance time ranges.
 * It monitors attendance settings and starts/stops QR generation based on configured times.
 */

import { supabase } from "@/integrations/supabase/client";
import { generateSecureQRCode, getQRExpirySeconds } from "@/lib/services/qr-security-api";

interface AttendanceTimeSettings {
  id: string;
  recording_start_time: string;
  recording_end_time: string;
  school_id?: string;
}

interface Room {
  id: string;
  name: string;
  school_id: string;
  block_id: string;
  current_qr_code?: string;
  qr_expiry?: string;
}

interface AutoQRSession {
  schoolId: string;
  isActive: boolean;
  startTime: string;
  endTime: string;
  intervalId?: NodeJS.Timeout;
  rooms: Room[];
}

class AutomaticQRService {
  private sessions: Map<string, AutoQRSession> = new Map();
  private monitoringInterval?: NodeJS.Timeout;
  private isInitialized = false;

  /**
   * Initialize the automatic QR service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log("🤖 Initializing Automatic QR Generation Service...");

    try {
      // Start monitoring attendance time ranges
      await this.startMonitoring();
      this.isInitialized = true;
      console.log("✅ Automatic QR Service initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize Automatic QR Service:", error);
      throw error;
    }
  }

  /**
   * Start monitoring attendance time ranges for all schools
   */
  private async startMonitoring(): Promise<void> {
    // Check every 1 second for immediate response to time changes
    this.monitoringInterval = setInterval(async () => {
      await this.checkAndUpdateSessions();
    }, 1000); // 1 second for immediate response

    // Initial check
    await this.checkAndUpdateSessions();
  }

  /**
   * Check attendance time ranges and update QR generation sessions
   */
  private async checkAndUpdateSessions(): Promise<void> {
    try {
      console.log("🔍 Checking attendance time ranges...");

      // Get attendance time settings from the correct attendance_settings table
      const { data: attendanceSettings, error } = await supabase
        .from("attendance_settings")
        .select("id, recording_start_time, recording_end_time");

      if (error) {
        console.error("❌ Error fetching attendance settings:", error);
        return;
      }

      if (!attendanceSettings || attendanceSettings.length === 0) {
        console.log("ℹ️ No attendance settings found");
        return;
      }

      console.log(`📋 Found ${attendanceSettings.length} attendance settings`);

      // Process each attendance setting
      // Note: The attendance_settings table doesn't have school_id, so we need to get all schools
      const { data: schools, error: schoolsError } = await supabase
        .from("schools")
        .select("id");

      if (schoolsError) {
        console.error("❌ Error fetching schools:", schoolsError);
        return;
      }

      // For now, apply the first attendance setting to all schools
      // TODO: In the future, you might want to add school_id to attendance_settings table
      const setting = attendanceSettings[0];

      for (const school of schools || []) {
        await this.processSchoolSettings({
          id: setting.id,
          recording_start_time: setting.recording_start_time,
          recording_end_time: setting.recording_end_time,
          school_id: school.id,
        });
      }
    } catch (error) {
      console.error("❌ Error in checkAndUpdateSessions:", error);
    }
  }

  /**
   * Process attendance settings for a specific school
   */
  private async processSchoolSettings(settings: AttendanceTimeSettings): Promise<void> {
    const schoolId = settings.school_id;
    if (!schoolId) {
      console.warn("⚠️ School ID missing in attendance settings");
      return;
    }

    const now = new Date();
    const isWithinTimeRange = this.isWithinAttendanceTime(
      settings.recording_start_time,
      settings.recording_end_time,
      now
    );

    const existingSession = this.sessions.get(schoolId);

    if (isWithinTimeRange && !existingSession?.isActive) {
      // Start QR generation for this school
      await this.startQRGenerationForSchool(schoolId, settings);
    } else if (!isWithinTimeRange && existingSession?.isActive) {
      // Stop QR generation for this school
      await this.stopQRGenerationForSchool(schoolId);
    }
  }

  /**
   * Check if current time is within attendance recording hours
   */
  private isWithinAttendanceTime(startTime: string, endTime: string, now: Date): boolean {
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();

    // Convert settings times to hours and minutes
    const startTimeParts = startTime.split(":");
    const endTimeParts = endTime.split(":");

    const startHours = parseInt(startTimeParts[0], 10);
    const startMinutes = parseInt(startTimeParts[1], 10);

    const endHours = parseInt(endTimeParts[0], 10);
    const endMinutes = parseInt(endTimeParts[1], 10);

    // Convert all to minutes for easier comparison
    const currentTotalMinutes = currentHours * 60 + currentMinutes;
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    return (
      currentTotalMinutes >= startTotalMinutes &&
      currentTotalMinutes <= endTotalMinutes
    );
  }

  /**
   * Start automatic QR generation for a school
   */
  private async startQRGenerationForSchool(
    schoolId: string,
    settings: AttendanceTimeSettings
  ): Promise<void> {
    try {
      console.log(`🚀 Starting automatic QR generation for school ${schoolId}`);

      // Get all rooms for this school
      const { data: rooms, error } = await supabase
        .from("rooms")
        .select("id, name, school_id, block_id, current_qr_code, qr_expiry")
        .eq("school_id", schoolId);

      if (error) {
        console.error(`❌ Error fetching rooms for school ${schoolId}:`, error);
        return;
      }

      if (!rooms || rooms.length === 0) {
        console.log(`ℹ️ No rooms found for school ${schoolId}`);
        return;
      }

      // Create session
      const session: AutoQRSession = {
        schoolId,
        isActive: true,
        startTime: settings.recording_start_time,
        endTime: settings.recording_end_time,
        rooms: rooms as Room[],
      };

      // Generate initial QR codes for all rooms IMMEDIATELY
      console.log(`🚀 Generating initial QR codes immediately for ${session.rooms.length} rooms`);
      await this.generateQRCodesForAllRooms(session);

      // Calculate smart interval for seamless transitions
      const qrExpirySeconds = getQRExpirySeconds();
      // Generate new QR when 75% of time has passed to ensure fresh QR is ready at 1 second
      const intervalSeconds = Math.max(3, Math.floor(qrExpirySeconds * 0.75));

      console.log(`📅 QR expiry: ${qrExpirySeconds}s, Generation interval: ${intervalSeconds}s (75% overlap for 1-second transition)`);

      // Set up continuous generation with overlap for seamless transitions
      session.intervalId = setInterval(async () => {
        console.log(`🔄 Scheduled QR generation for school ${session.schoolId}`);
        await this.generateQRCodesForAllRooms(session);
      }, intervalSeconds * 1000);

      this.sessions.set(schoolId, session);
      console.log(`✅ Automatic QR generation started for school ${schoolId}`);
    } catch (error) {
      console.error(`❌ Error starting QR generation for school ${schoolId}:`, error);
    }
  }

  /**
   * Stop automatic QR generation for a school
   */
  private async stopQRGenerationForSchool(schoolId: string): Promise<void> {
    try {
      console.log(`🛑 Stopping automatic QR generation for school ${schoolId}`);

      const session = this.sessions.get(schoolId);
      if (!session) return;

      // Clear interval
      if (session.intervalId) {
        clearInterval(session.intervalId);
      }

      // Mark session as inactive
      session.isActive = false;
      this.sessions.set(schoolId, session);

      // Optionally clear QR codes from rooms (or let them expire naturally)
      // await this.clearQRCodesForSchool(schoolId);

      console.log(`✅ Automatic QR generation stopped for school ${schoolId}`);
    } catch (error) {
      console.error(`❌ Error stopping QR generation for school ${schoolId}:`, error);
    }
  }

  /**
   * Generate QR codes for all rooms in a session
   */
  private async generateQRCodesForAllRooms(session: AutoQRSession): Promise<void> {
    console.log(`🔄 Generating QR codes for ${session.rooms.length} rooms in school ${session.schoolId}`);

    const promises = session.rooms.map(room => this.generateQRCodeForRoom(room));
    
    try {
      await Promise.allSettled(promises);
      console.log(`✅ QR code generation completed for school ${session.schoolId}`);
    } catch (error) {
      console.error(`❌ Error in batch QR generation for school ${session.schoolId}:`, error);
    }
  }

  /**
   * Generate QR code for a specific room
   */
  private async generateQRCodeForRoom(room: Room): Promise<void> {
    try {
      // Get configurable expiry time from environment
      const qrExpirySeconds = getQRExpirySeconds();

      // For seamless transitions, always generate new QR codes
      // This ensures there's always a fresh QR ready before the current one expires
      console.log(`🔄 Generating QR code for room ${room.name} (expiry: ${qrExpirySeconds}s)`);

      if (room.current_qr_code && room.qr_expiry) {
        const expiry = new Date(room.qr_expiry);
        const timeUntilExpiry = expiry.getTime() - Date.now();
        console.log(`📊 Room ${room.name} current QR expires in ${Math.floor(timeUntilExpiry / 1000)}s, generating new one for seamless transition`);
      }

      console.log(`🔄 Generating QR code for room ${room.name} (expiry: ${qrExpirySeconds}s)`);

      // Get current user session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      const userId = session?.user?.id;

      console.log(`🔧 Debug - Expiry seconds being passed: ${qrExpirySeconds}`);
      console.log(`🔧 Debug - User ID: ${userId}`);

      // Generate new QR code using configurable expiry time
      const qrData = await generateSecureQRCode(
        room.id,
        room.school_id,
        room.block_id,
        qrExpirySeconds, // Use configurable expiry time from .env
        userId // Use current user ID or undefined for fallback
      );

      // Verify the actual expiry time in the generated QR
      const actualExpiry = new Date(qrData.expires_at);
      const actualDuration = Math.floor((actualExpiry.getTime() - new Date(qrData.timestamp).getTime()) / 1000);

      console.log(`✅ QR code generated for room ${room.name}`);
      console.log(`🔧 Debug - Expected expiry: ${qrExpirySeconds}s`);
      console.log(`🔧 Debug - Actual expiry: ${actualDuration}s`);
      console.log(`🔧 Debug - Match: ${actualDuration === qrExpirySeconds ? "✅ YES" : "❌ NO"}`);
    } catch (error) {
      console.error(`❌ Error generating QR code for room ${room.name}:`, error);
    }
  }

  /**
   * Get current session status for a school
   */
  getSessionStatus(schoolId: string): AutoQRSession | null {
    return this.sessions.get(schoolId) || null;
  }

  /**
   * Get all active sessions
   */
  getAllActiveSessions(): AutoQRSession[] {
    return Array.from(this.sessions.values()).filter(session => session.isActive);
  }

  /**
   * Manually trigger QR generation for a school (for testing)
   */
  async manualTrigger(schoolId: string): Promise<void> {
    const session = this.sessions.get(schoolId);
    if (session && session.isActive) {
      await this.generateQRCodesForAllRooms(session);
    }
  }

  /**
   * Force immediate QR generation for all schools (used when attendance starts)
   */
  async forceImmediateGeneration(): Promise<void> {
    console.log("🚀 Force immediate QR generation triggered");

    // First check and update sessions to ensure we have active sessions
    await this.checkAndUpdateSessions();

    // Then generate QR codes for all active sessions
    for (const [schoolId, session] of this.sessions.entries()) {
      if (session.isActive) {
        console.log(`🚀 Force generating QR codes for school ${schoolId}`);
        await this.generateQRCodesForAllRooms(session);
      }
    }
  }

  /**
   * Stop the service and cleanup
   */
  stop(): void {
    console.log("🛑 Stopping Automatic QR Service...");

    // Clear monitoring interval
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    // Stop all school sessions
    for (const [schoolId, session] of this.sessions.entries()) {
      if (session.intervalId) {
        clearInterval(session.intervalId);
      }
    }

    this.sessions.clear();
    this.isInitialized = false;
    console.log("✅ Automatic QR Service stopped");
  }
}

// Export singleton instance
export const automaticQRService = new AutomaticQRService();

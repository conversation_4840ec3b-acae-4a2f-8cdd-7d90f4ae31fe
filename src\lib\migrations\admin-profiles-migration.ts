import { supabase } from "@/lib/supabase";

// Function to check if admin_profiles table exists and create it if it doesn't
const ensureAdminProfilesTableExists = async () => {
  try {
    // First, ensure the school column exists in the profiles table
    try {
      // Ensure school column exists in profiles table

      // Call the add_school_column function
      const { error: addColumnError } = await supabase.rpc("add_school_column");

      if (addColumnError) {
        console.error("Error calling add_school_column:", addColumnError);

        // Try a direct approach - update a profile with a school value
        try {
          // First check if we can select from profiles
          const { data: profilesData, error: profilesError } = await supabase
            .from("profiles")
            .select("id")
            .limit(1);

          if (profilesError) {
            console.error("Error checking profiles table:", profilesError);
          } else if (profilesData && profilesData.length > 0) {
            // Try to update a profile with a school value to see if the column exists
            const { error: updateError } = await supabase
              .from("profiles")
              .update({ school: "Test School" })
              .eq("id", profilesData[0].id)
              .select();

            if (updateError && updateError.code === "42703") {
              console.log(
                "School column doesn't exist, trying to add it directly..."
              );

              // Try to add the column directly through a profile update
              try {
                // This will fail but it's a way to check if the column exists
                await supabase.from("profiles").select("school").limit(1);

                console.log("School column appears to exist");
              } catch (selectError) {
                console.error("Error checking school column:", selectError);
              }
            } else {
              console.log("School column exists (verified by update)");
            }
          }
        } catch (directError) {
          console.error("Error in direct school column check:", directError);
        }
      }
    } catch (columnError) {
      console.error("Error ensuring school column exists:", columnError);
      // Continue anyway, as this is not critical
    }

    // Now set up the admin_profiles table using the setup_admin_profiles function

    try {
      const { error: setupError } = await supabase.rpc("setup_admin_profiles");

      if (setupError) {
        console.error("Error calling setup_admin_profiles:", setupError);

        // Try a direct approach - check if we can select from admin_profiles
        try {
          const { error: selectError } = await supabase
            .from("admin_profiles")
            .select("id")
            .limit(1);

          if (selectError && selectError.code === "42P01") {
            console.log("admin_profiles table doesn't exist, creating it...");
            await createAdminProfilesTable();
          } else {
            console.log("admin_profiles table exists (verified by select)");
          }
        } catch (selectError) {
          console.error("Error checking admin_profiles table:", selectError);
          console.log("Attempting to create admin_profiles table...");
          await createAdminProfilesTable();
        }
      }

      return true;
    } catch (setupError) {
      console.error("Error in admin_profiles setup:", setupError);

      // Try to create the table anyway
      await createAdminProfilesTable();
      return true;
    }
  } catch (error) {
    console.error("Error ensuring admin_profiles table exists:", error);

    // Try to create the table anyway as a last resort
    try {
      await createAdminProfilesTable();
      return true;
    } catch (createError) {
      console.error("Final error creating admin_profiles table:", createError);
      return false;
    }
  }
};

// Helper function to create the admin_profiles table
const createAdminProfilesTable = async () => {
  try {
    console.log(
      "Setting up admin_profiles table using setup_admin_profiles function..."
    );

    // Use the setup_admin_profiles function to create the table and policies
    const { error: setupError } = await supabase.rpc("setup_admin_profiles");

    if (setupError) {
      console.error("Error calling setup_admin_profiles function:", setupError);

      // Fallback to direct SQL if the function call fails
      console.log(
        "Falling back to direct SQL for admin_profiles table creation..."
      );

      try {
        // First check if the table exists
        const { data: tableExists, error: checkError } = await supabase
          .from("pg_tables")
          .select("tablename")
          .eq("schemaname", "public")
          .eq("tablename", "admin_profiles")
          .maybeSingle();

        if (checkError) {
          console.error(
            "Error checking if admin_profiles table exists:",
            checkError
          );
        }

        if (!tableExists) {
          console.log("Creating admin_profiles table directly...");

          // Create the table directly
          const { error: createError } = await supabase
            .from("admin_profiles")
            .insert({
              id: "00000000-0000-0000-0000-000000000000",
              user_id: "00000000-0000-0000-0000-000000000000",
              admin_id: "DUMMY",
              position: "DUMMY",
              school: "DUMMY",
            })
            .select();

          if (createError && createError.code !== "23505") {
            console.error("Error creating admin_profiles table:", createError);
          } else {
            console.log("admin_profiles table created or already exists");

            // Delete the dummy record
            try {
              await supabase
                .from("admin_profiles")
                .delete()
                .eq("id", "00000000-0000-0000-0000-000000000000");
            } catch (deleteError) {
              console.error("Error deleting dummy record:", deleteError);
            }
          }
        } else {
          console.log("admin_profiles table already exists");
        }
      } catch (fallbackError) {
        console.error(
          "Error in fallback admin_profiles creation:",
          fallbackError
        );
      }

      return true; // Continue anyway
    }

    console.log("admin_profiles table and policies set up successfully");
    return true;
  } catch (error) {
    console.error("Error in createAdminProfilesTable:", error);
    return false;
  }
};

export const migrateAdminProfiles = async () => {
  try {
    // First ensure the table exists
    const tableExists = await ensureAdminProfilesTableExists();
    if (!tableExists) {
      console.log(
        "Could not ensure admin_profiles table exists, but continuing anyway"
      );
    }

    // Start admin profiles migration

    // First, ensure all admin profiles have the school field populated
    // This is a critical step to make sure schools are available for selection
    try {
      // Get all admin profiles from the profiles table
      const { data: adminProfiles, error: fetchError } = await supabase
        .from("profiles")
        .select("*")
        .eq("role", "admin");

      if (fetchError) {
        console.error("Error fetching admin profiles:", fetchError);
        return;
      }

      if (!adminProfiles || adminProfiles.length === 0) {
        console.log("No admin profiles found to migrate");
        return;
      }

      // Process admin profiles

      // Log the school values for debugging
      const schoolValues = adminProfiles
        .map((profile) => profile.school)
        .filter(Boolean);

      // Check for school values in admin profiles

      // If there are no school values, log a warning
      if (schoolValues.length === 0) {
        console.warn(
          "WARNING: No school values found in admin profiles. Please ensure admins add school names to their profiles."
        );
      }

      // For each admin profile, check if it exists in the admin_profiles table
      for (const profile of adminProfiles) {
        // Skip if the profile doesn't have position or school
        if (!profile.position || !profile.school) {
          console.log(
            `Admin profile ${profile.id} is missing position or school, skipping`
          );
          continue;
        }

        try {
          // First, make sure the school field is populated in the profiles table
          // This ensures schools can be fetched even if admin_profiles table has issues
          // Ensure school field is populated for admin

          // Try to insert into admin_profiles table
          try {
            const { data: existingAdminProfile, error: checkError } =
              await supabase
                .from("admin_profiles")
                .select("id")
                .eq("id", profile.id)
                .maybeSingle();

            if (checkError) {
              if (checkError.code !== "PGRST116") {
                console.error(
                  `Error checking admin profile for ${profile.id}:`,
                  checkError
                );
              }
              // Continue anyway, try to insert
            }

            // If the admin profile doesn't exist in the admin_profiles table
            if (!existingAdminProfile) {
              try {
                const { error: insertError } = await supabase
                  .from("admin_profiles")
                  .insert({
                    id: profile.id,
                    user_id: profile.user_id,
                    admin_id: profile.admin_id || `A-${Date.now()}`,
                    position: profile.position,
                    school: profile.school,
                  });

                if (insertError) {
                  console.error(
                    `Error creating admin profile for ${profile.id}:`,
                    insertError
                  );
                } else {
                  console.log(`Migrated admin profile for ${profile.id}`);
                }
              } catch (insertCatchError) {
                console.error(
                  `Exception creating admin profile for ${profile.id}:`,
                  insertCatchError
                );
              }
            }
          } catch (profileError) {
            console.error(
              `Error processing admin profile ${profile.id}:`,
              profileError
            );
          }
        } catch (schoolUpdateError) {
          console.error(
            `Error ensuring school field for admin ${profile.id}:`,
            schoolUpdateError
          );
        }
      }
    } catch (adminProfilesError) {
      console.error("Error processing admin profiles:", adminProfilesError);
    }

    // Admin profiles migration completed
  } catch (error) {
    console.error("Error in admin profiles migration:", error);
  }
};

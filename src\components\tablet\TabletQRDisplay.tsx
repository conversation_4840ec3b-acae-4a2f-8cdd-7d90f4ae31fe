import { useState, useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import QRCode from "react-qr-code";
// Import websocket service with error handling
import type {
  QRUpdate,
  AttendanceUpdate,
} from "@/lib/services/websocket-service";
import { supabase } from "@/integrations/supabase/client";
import { Clock, Users, Wifi, WifiOff, Shield, CheckCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

interface TabletQRDisplayProps {
  roomId: string;
  schoolId: string;
}

interface RoomInfo {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  capacity: number;
  block_name?: string;
}

export default function TabletQRDisplay({
  roomId,
  schoolId,
}: TabletQRDisplayProps) {
  const { t } = useTranslation();

  // WebSocket service with fallback
  const [websocketService, setWebsocketService] = useState<any>(null);

  // QR Code state
  const [qrData, setQrData] = useState<string>("");
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isActive, setIsActive] = useState(false);

  // Room and attendance state
  const [roomInfo, setRoomInfo] = useState<RoomInfo | null>(null);
  const [attendanceCount, setAttendanceCount] = useState<number>(0);
  const [recentAttendance, setRecentAttendance] = useState<AttendanceUpdate[]>(
    []
  );

  // Connection state
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Auto-refresh timer
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize websocket service
  useEffect(() => {
    const initWebSocket = async () => {
      try {
        const wsModule = await import("@/lib/services/websocket-service");
        setWebsocketService(wsModule.websocketService);
        setIsConnected(true);
      } catch (error) {
        console.warn("WebSocket service not available, using polling fallback");
        setIsConnected(false);
      }
    };

    initWebSocket();
  }, []);

  // Fetch room information
  useEffect(() => {
    const fetchRoomInfo = async () => {
      try {
        const { data: room, error } = await supabase
          .from("rooms")
          .select(
            `
            id,
            name,
            building,
            floor,
            capacity,
            current_qr_code,
            qr_expiry,
            blocks (
              name
            )
          `
          )
          .eq("id", roomId)
          .single();

        if (error) throw error;

        setRoomInfo({
          id: room.id,
          name: room.name,
          building: room.building,
          floor: room.floor,
          capacity: room.capacity,
          block_name: room.blocks?.name,
        });

        // Load existing QR code if available
        if (room.current_qr_code && room.qr_expiry) {
          const expiry = new Date(room.qr_expiry);
          if (expiry.getTime() > Date.now()) {
            setQrData(room.current_qr_code);
            setQrExpiry(expiry);
            setIsActive(true);
          }
        }
      } catch (error) {
        console.error("Error fetching room info:", error);
      }
    };

    fetchRoomInfo();
  }, [roomId]);

  // Subscribe to real-time QR updates
  useEffect(() => {
    if (!schoolId || !roomId || !websocketService) return;

    let unsubscribeQR: (() => void) | undefined;

    try {
      unsubscribeQR = websocketService.subscribeToQRUpdates(
        schoolId,
        (update: QRUpdate) => {
          if (update.data.room_id === roomId) {
            console.log("QR Update received:", update);

            if (
              update.type === "qr_generated" ||
              update.type === "qr_refreshed"
            ) {
              // Fetch the latest QR code from database
              fetchLatestQRCode();
            } else if (update.type === "qr_expired") {
              setIsActive(false);
              setQrData("");
              setQrExpiry(null);
            }

            setLastUpdate(new Date());
            setIsConnected(true);
          }
        },
        roomId
      );
    } catch (error) {
      console.warn("Failed to subscribe to QR updates:", error);
      setIsConnected(false);
    }

    return () => {
      if (unsubscribeQR) {
        try {
          unsubscribeQR();
        } catch (error) {
          console.warn("Error unsubscribing from QR updates:", error);
        }
      }
    };
  }, [schoolId, roomId, websocketService]);

  // Subscribe to real-time attendance updates
  useEffect(() => {
    if (!schoolId || !roomId || !websocketService) return;

    let unsubscribeAttendance: (() => void) | undefined;

    try {
      unsubscribeAttendance = websocketService.subscribeToAttendance(
        schoolId,
        (update: AttendanceUpdate) => {
          if (update.data.room_id === roomId) {
            console.log("Attendance update received:", update);

            // Add to recent attendance
            setRecentAttendance((prev) => [update, ...prev.slice(0, 4)]); // Keep last 5
            setAttendanceCount((prev) => prev + 1);

            // Show visual feedback
            showAttendanceNotification(update.data.student_name || "Student");
          }
        },
        roomId
      );
    } catch (error) {
      console.warn("Failed to subscribe to attendance updates:", error);
      setIsConnected(false);
    }

    return () => {
      if (unsubscribeAttendance) {
        try {
          unsubscribeAttendance();
        } catch (error) {
          console.warn("Error unsubscribing from attendance updates:", error);
        }
      }
    };
  }, [schoolId, roomId, websocketService]);

  // Polling fallback when websocket is not available
  useEffect(() => {
    if (!websocketService && roomId) {
      const pollInterval = setInterval(() => {
        fetchLatestQRCode();
      }, 5000); // Poll every 5 seconds for faster response

      return () => clearInterval(pollInterval);
    }
  }, [websocketService, roomId]);

  // Fetch latest QR code from database
  const fetchLatestQRCode = async () => {
    try {
      setIsRefreshing(true);
      console.log(`Fetching latest QR code for room ${roomId}`);

      const { data: room, error } = await supabase
        .from("rooms")
        .select("current_qr_code, qr_expiry")
        .eq("id", roomId)
        .single();

      if (error) throw error;

      if (room.current_qr_code && room.qr_expiry) {
        const expiry = new Date(room.qr_expiry);
        if (expiry.getTime() > Date.now()) {
          console.log(
            `Updating tablet display with new QR code for room ${roomId}`
          );
          setQrData(room.current_qr_code);
          setQrExpiry(expiry);
          setIsActive(true);
          setTimeLeft(Math.floor((expiry.getTime() - Date.now()) / 1000));
        } else {
          console.log(`QR code for room ${roomId} has expired`);
          setIsActive(false);
          setQrData("");
          setQrExpiry(null);
        }
      } else {
        console.log(`No QR code found for room ${roomId}`);
        setIsActive(false);
        setQrData("");
        setQrExpiry(null);
      }
    } catch (error) {
      console.error("Error fetching QR code:", error);
      setIsConnected(false);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Timer for countdown with proactive refresh
  useEffect(() => {
    if (qrExpiry && isActive) {
      timerRef.current = setInterval(() => {
        const remaining = Math.max(
          0,
          Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
        );
        setTimeLeft(remaining);

        // Proactively fetch new QR code when close to expiry
        if (remaining <= 10 && remaining > 0) {
          console.log(
            `Tablet: Proactively checking for new QR code with ${remaining}s remaining`
          );
          fetchLatestQRCode();
        } else if (remaining <= 0) {
          // Only set inactive if no new QR code was received
          console.log("Tablet: QR code expired, checking for replacement");
          fetchLatestQRCode();

          // Give a brief moment for new QR code to arrive before showing inactive state
          setTimeout(() => {
            const currentRemaining = Math.max(
              0,
              Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
            );
            if (currentRemaining <= 0) {
              setIsActive(false);
              setQrData("");
              setQrExpiry(null);
            }
          }, 2000); // 2 second grace period
        }
      }, 1000);

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }
  }, [qrExpiry, isActive]);

  // Show attendance notification
  const showAttendanceNotification = (studentName: string) => {
    // Create a temporary visual notification
    const notification = document.createElement("div");
    notification.className =
      "fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 animate-pulse";

    const checkedInText = t("admin.tablets.display.checkedIn");
    notification.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span>${studentName} ${checkedInText}</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  };

  // Format time remaining
  const formatTimeLeft = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 text-sm">
      {isConnected ? (
        <>
          <Wifi className="w-4 h-4 text-green-600" />
          <span className="text-green-600">{t("admin.tablets.display.connected")}</span>
        </>
      ) : (
        <>
          <WifiOff className="w-4 h-4 text-red-600" />
          <span className="text-red-600">{t("admin.tablets.display.disconnected")}</span>
        </>
      )}
      <span className="text-gray-500">
        {t("admin.tablets.display.lastUpdate")} {lastUpdate.toLocaleTimeString()}
      </span>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl font-bold text-gray-800">
                  {roomInfo?.name || t("admin.tablets.display.loading")}
                </CardTitle>
                <p className="text-gray-600">
                  {roomInfo?.block_name && `Block ${roomInfo.block_name}`}
                  {roomInfo?.building && ` • ${roomInfo.building}`}
                  {roomInfo?.floor && ` • Floor ${roomInfo.floor}`}
                </p>
              </div>
              <div className="text-right">
                <ConnectionStatus />
                <div className="flex items-center gap-2 mt-1">
                  <Users className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-600">
                    {t("admin.tablets.display.capacity")} {roomInfo?.capacity || 0}
                  </span>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* QR Code Display */}
        <Card className="text-center">
          <CardHeader>
            <CardTitle className="flex items-center justify-center gap-2">
              <Shield className="w-6 h-6 text-blue-600" />
              {t("admin.tablets.display.attendanceQRCode")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {isActive && qrData ? (
              <>
                {/* QR Code */}
                <div className="flex justify-center">
                  <div className="bg-white p-8 rounded-2xl shadow-lg border-4 border-blue-200">
                    <QRCode
                      value={qrData}
                      size={300}
                      style={{
                        height: "auto",
                        maxWidth: "100%",
                        width: "100%",
                      }}
                    />
                  </div>
                </div>

                {/* Timer */}
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <Clock className="w-5 h-5 text-blue-600" />
                    <span className="text-lg font-medium">{t("admin.tablets.display.timeRemaining")}</span>
                  </div>
                  <div
                    className={`text-4xl font-bold ${
                      timeLeft < 60
                        ? "text-red-600 animate-pulse"
                        : "text-blue-600"
                    }`}
                  >
                    {formatTimeLeft(timeLeft)}
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Badge variant={timeLeft < 60 ? "destructive" : "default"}>
                      {timeLeft < 60 ? t("admin.tablets.display.expiringSoon") : t("admin.tablets.display.active")}
                    </Badge>
                    {isRefreshing && (
                      <Badge variant="secondary" className="animate-pulse">
                        {t("admin.tablets.display.refreshing")}
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Instructions */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-blue-800 font-medium">
                    {t("admin.tablets.display.scanQRInstruction")}
                  </p>
                  <p className="text-blue-600 text-sm mt-1">
                    {t("admin.tablets.display.roomVerification")}
                  </p>
                </div>
              </>
            ) : (
              <div className="py-12">
                <div className="text-gray-400 mb-4">
                  <Shield className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-medium text-gray-600 mb-2">
                  {t("admin.tablets.display.noActiveQR")}
                </h3>
                <p className="text-gray-500">
                  {t("admin.tablets.display.waitingForSession")}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Live Attendance Feed */}
        {recentAttendance.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                {t("admin.tablets.display.recentCheckins")} ({attendanceCount})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {recentAttendance.map((attendance, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-200"
                  >
                    <span className="font-medium text-green-800">
                      {attendance.data.student_name}
                    </span>
                    <span className="text-green-600 text-sm">
                      {new Date(attendance.data.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, MapPin, Building, Home } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { createLocationVerificationSettingsTable } from "@/lib/migrations.new";
import { useTranslation } from "react-i18next";

interface LocationVerificationSettingsProps {
  roomId: string;
  blockId: string;
}

interface LocationSettings {
  globalEnabled: boolean;
  blockEnabled: boolean;
  roomEnabled: boolean;
  blockId: string;
  roomId: string;
  blockRadiusMeters: number;
  roomRadiusMeters: number;
}

export default function AdminLocationVerificationSettings({
  roomId,
  blockId,
}: LocationVerificationSettingsProps) {
  const [settings, setSettings] = useState<LocationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();
  const { t } = useTranslation();

  // Ensure the location_verification_settings table exists
  const ensureTableExists = async (): Promise<boolean> => {
    try {
      return await createLocationVerificationSettingsTable();
    } catch (error) {
      console.error(
        "Error ensuring location verification settings table exists:",
        error
      );
      return true; // Return true anyway to let the app continue
    }
  };

  // Define fetchSettings function outside useEffect so it can be called from other places
  const fetchSettings = async () => {
    if (!roomId) return;

    setLoading(true);
    try {
      // Ensure the table exists first
      await ensureTableExists();

      console.log("Fetching settings for room:", roomId);

      // First, try to get direct settings from the database - get the latest entries
      const { data: globalSettings, error: globalError } = await supabase
        .from("location_verification_settings")
        .select("*")
        .is("block_id", null)
        .is("room_id", null)
        .order("updated_at", { ascending: false })
        .limit(1);

      const { data: blockSettings, error: blockError } = await supabase
        .from("location_verification_settings")
        .select("*")
        .eq("block_id", blockId)
        .is("room_id", null)
        .order("updated_at", { ascending: false })
        .limit(1);

      const { data: roomSettings, error: roomError } = await supabase
        .from("location_verification_settings")
        .select("*")
        .eq("room_id", roomId)
        .order("updated_at", { ascending: false })
        .limit(1);

      if (globalError || blockError || roomError) {
        console.error("Error fetching settings:", {
          globalError,
          blockError,
          roomError,
        });
      } else {
        console.log("Settings found:", {
          globalSettings,
          blockSettings,
          roomSettings,
        });

        // Get the latest settings at each level
        const globalSetting =
          globalSettings && globalSettings.length > 0
            ? globalSettings[0]
            : null;
        const blockSetting =
          blockSettings && blockSettings.length > 0 ? blockSettings[0] : null;
        const roomSetting =
          roomSettings && roomSettings.length > 0 ? roomSettings[0] : null;

        setSettings({
          globalEnabled: globalSetting?.global_enabled ?? true,
          blockEnabled: blockSetting?.block_enabled ?? true,
          roomEnabled: roomSetting?.room_enabled ?? true,
          blockId: blockId,
          roomId: roomId,
          blockRadiusMeters: 100, // We'll get these from locations
          roomRadiusMeters: 50,
        });

        setLoading(false);
        return;
      }

      // If direct query fails or returns no results, fall back to the RPC
      console.log("Falling back to RPC function");
      const { data, error } = await supabase.rpc(
        "get_location_verification_settings",
        {
          room_uuid: roomId,
        }
      );

      if (error) {
        console.error("Error from RPC call:", error);
        // Set default settings
        setSettings({
          globalEnabled: true,
          blockEnabled: true,
          roomEnabled: true,
          blockId: blockId,
          roomId: roomId,
          blockRadiusMeters: 100,
          roomRadiusMeters: 50,
        });
        setLoading(false);
        return;
      }

      if (data && data.length > 0) {
        console.log("RPC returned data:", data);
        setSettings({
          globalEnabled: data[0].global_enabled,
          blockEnabled: data[0].block_enabled,
          roomEnabled: data[0].room_enabled,
          blockId: data[0].block_id,
          roomId: data[0].room_id,
          blockRadiusMeters: data[0].block_radius_meters,
          roomRadiusMeters: data[0].room_radius_meters,
        });
      } else {
        console.log("No data returned from RPC, using defaults");
        // No data returned, use defaults
        setSettings({
          globalEnabled: true,
          blockEnabled: true,
          roomEnabled: true,
          blockId: blockId,
          roomId: roomId,
          blockRadiusMeters: 100,
          roomRadiusMeters: 50,
        });
      }
    } catch (error) {
      console.error("Error fetching location verification settings:", error);

      // Set default settings
      setSettings({
        globalEnabled: true,
        blockEnabled: true,
        roomEnabled: true,
        blockId: blockId,
        roomId: roomId,
        blockRadiusMeters: 100,
        roomRadiusMeters: 50,
      });

      toast({
        title: t("common.error"),
        description: t("admin.settings.locationSettingsLoadError"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch current settings when component mounts or roomId/blockId changes
  useEffect(() => {
    fetchSettings();
  }, [roomId, blockId]);

  // Set up real-time subscription to settings changes
  useEffect(() => {
    if (!roomId) return;

    // Subscribe to changes in the location_verification_settings table
    const channel = supabase
      .channel("admin-location-settings")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "location_verification_settings",
        },
        (payload) => {
          console.log("Location settings changed:", payload);
          // Refresh settings when changes are detected
          fetchSettings();
        }
      )
      .subscribe();

    // Clean up subscription when component unmounts
    return () => {
      supabase.removeChannel(channel);
    };
  }, [roomId, blockId]);

  // Save settings
  const saveSettings = async () => {
    if (!settings || !profile) return;

    setSaving(true);
    try {
      console.log("Saving settings:", settings);

      // First, get existing settings to determine if we need to update or insert
      const { data: existingGlobalSettings } = await supabase
        .from("location_verification_settings")
        .select("id")
        .is("block_id", null)
        .is("room_id", null)
        .order("updated_at", { ascending: false })
        .limit(1);

      const { data: existingBlockSettings } = await supabase
        .from("location_verification_settings")
        .select("id")
        .eq("block_id", settings.blockId)
        .is("room_id", null)
        .order("updated_at", { ascending: false })
        .limit(1);

      const { data: existingRoomSettings } = await supabase
        .from("location_verification_settings")
        .select("id")
        .eq("room_id", settings.roomId)
        .order("updated_at", { ascending: false })
        .limit(1);

      // Update global settings
      if (existingGlobalSettings && existingGlobalSettings.length > 0) {
        // Update existing record
        const { error: globalError } = await supabase
          .from("location_verification_settings")
          .update({
            global_enabled: settings.globalEnabled,
            updated_by: profile.id,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingGlobalSettings[0].id);

        if (globalError) {
          console.error("Error updating global settings:", globalError);
          throw globalError;
        }
      } else {
        // Insert new record
        const { error: globalError } = await supabase
          .from("location_verification_settings")
          .insert({
            global_enabled: settings.globalEnabled,
            block_id: null,
            room_id: null,
            updated_by: profile.id,
            updated_at: new Date().toISOString(),
          });

        if (globalError) {
          console.error("Error inserting global settings:", globalError);
          throw globalError;
        }
      }

      // Update block settings
      if (settings.blockId) {
        console.log("Saving block settings for block:", settings.blockId);

        if (existingBlockSettings && existingBlockSettings.length > 0) {
          // Update existing record
          const { error: blockError } = await supabase
            .from("location_verification_settings")
            .update({
              block_enabled: settings.blockEnabled,
              updated_by: profile.id,
              updated_at: new Date().toISOString(),
            })
            .eq("id", existingBlockSettings[0].id);

          if (blockError) {
            console.error("Error updating block settings:", blockError);
            throw blockError;
          }
        } else {
          // Insert new record
          const { error: blockError } = await supabase
            .from("location_verification_settings")
            .insert({
              block_enabled: settings.blockEnabled,
              block_id: settings.blockId,
              room_id: null,
              updated_by: profile.id,
              updated_at: new Date().toISOString(),
            });

          if (blockError) {
            console.error("Error inserting block settings:", blockError);
            throw blockError;
          }
        }
      }

      // Update room settings
      if (settings.roomId) {
        console.log("Saving room settings for room:", settings.roomId);

        if (existingRoomSettings && existingRoomSettings.length > 0) {
          // Update existing record
          const { error: roomError } = await supabase
            .from("location_verification_settings")
            .update({
              room_enabled: settings.roomEnabled,
              updated_by: profile.id,
              updated_at: new Date().toISOString(),
            })
            .eq("id", existingRoomSettings[0].id);

          if (roomError) {
            console.error("Error updating room settings:", roomError);
            throw roomError;
          }
        } else {
          // Insert new record
          const { error: roomError } = await supabase
            .from("location_verification_settings")
            .insert({
              room_enabled: settings.roomEnabled,
              block_id: settings.blockId,
              room_id: settings.roomId,
              updated_by: profile.id,
              updated_at: new Date().toISOString(),
            });

          if (roomError) {
            console.error("Error inserting room settings:", roomError);
            throw roomError;
          }
        }
      }

      // Success! Now refresh the settings to make sure we have the latest
      toast({
        title: t("common.success"),
        description: t("admin.settings.locationSettingsSaved"),
        variant: "default",
      });

      // Refresh settings after a short delay to allow the database to update
      setTimeout(() => {
        fetchSettings();
      }, 500);
    } catch (error) {
      console.error("Error saving location verification settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.settings.locationSettingsSaveError"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {t("admin.settings.locationVerificationSettings")}
        </CardTitle>
        <CardDescription>
          {t("admin.settings.configureVerificationRequirements")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Global Setting */}
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-muted-foreground" />
            <Label htmlFor="global-location" className="text-base">
              {t("admin.settings.enableLocationVerification")}
            </Label>
          </div>
          <Checkbox
            id="global-location"
            checked={settings?.globalEnabled}
            onCheckedChange={(checked) =>
              setSettings((prev) =>
                prev ? { ...prev, globalEnabled: checked as boolean } : null
              )
            }
            disabled={saving}
          />
        </div>
        <p className="text-sm text-muted-foreground pl-7">
          {t("admin.settings.whenEnabledVerifyLocation")}
        </p>

        {/* Block Setting */}
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <Building className="h-5 w-5 text-muted-foreground" />
            <Label htmlFor="block-location" className="text-base">
              {t("admin.settings.blockLevelVerification")}
            </Label>
          </div>
          <Checkbox
            id="block-location"
            checked={settings?.blockEnabled}
            onCheckedChange={(checked) =>
              setSettings((prev) =>
                prev ? { ...prev, blockEnabled: checked as boolean } : null
              )
            }
            disabled={saving || !settings?.globalEnabled}
          />
        </div>
        <p className="text-sm text-muted-foreground pl-7">
          {t("admin.settings.whenEnabledWithinBlockRadius", {
            radius: settings?.blockRadiusMeters,
          })}
        </p>

        {/* Room Setting */}
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <Home className="h-5 w-5 text-muted-foreground" />
            <Label htmlFor="room-location" className="text-base">
              {t("admin.settings.roomLevelVerification")}
            </Label>
          </div>
          <Checkbox
            id="room-location"
            checked={settings?.roomEnabled}
            onCheckedChange={(checked) =>
              setSettings((prev) =>
                prev ? { ...prev, roomEnabled: checked as boolean } : null
              )
            }
            disabled={
              saving || !settings?.globalEnabled || !settings?.blockEnabled
            }
          />
        </div>
        <p className="text-sm text-muted-foreground pl-7">
          {t("admin.settings.whenEnabledWithinRoomRadius", {
            radius: settings?.roomRadiusMeters,
          })}
        </p>

        {/* Save Button */}
        <Button
          onClick={saveSettings}
          disabled={saving || !settings}
          className="w-full mt-4"
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("admin.settings.saving")}
            </>
          ) : (
            t("admin.settings.saveSettings")
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

import { useState, useEffect } from "react";
import { Wifi, WifiOff, RefreshCw, ExternalLink } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useOnlineStatus } from "@/hooks/useOnlineStatus";

interface OfflineAlertProps {
  className?: string;
}

export function OfflineAlert({ className }: OfflineAlertProps) {
  const { isOnline, checkConnection, goToOfflinePage } = useOnlineStatus();
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [showRetryAnimation, setShowRetryAnimation] = useState<boolean>(false);

  useEffect(() => {
    // Update visibility based on online status
    if (isOnline) {
      // Keep the alert visible for a moment when coming back online
      setTimeout(() => setIsVisible(false), 3000);
    } else {
      setIsVisible(true);
    }
  }, [isOnline]);

  // Check initial status
  useEffect(() => {
    setIsVisible(!navigator.onLine);

    // Periodically check connection status
    const intervalId = setInterval(() => {
      checkConnection();
    }, 30000); // Check every 30 seconds

    return () => clearInterval(intervalId);
  }, [checkConnection]);

  const handleRetry = async () => {
    setShowRetryAnimation(true);

    // Try to reload the page or reconnect
    if (isOnline) {
      window.location.reload();
    } else {
      // Try to check connection
      const isConnected = await checkConnection();

      if (isConnected) {
        window.location.reload();
      } else {
        // Just show animation for visual feedback when offline
        setTimeout(() => setShowRetryAnimation(false), 1500);
      }
    }
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        transition={{ duration: 0.3 }}
        className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md ${className}`}
      >
        <Alert
          className={`shadow-lg border-2 ${
            isOnline
              ? "bg-green-50 border-green-200"
              : "bg-amber-50 border-amber-200"
          }`}
        >
          <div className="flex items-start">
            {isOnline ? (
              <Wifi className="h-5 w-5 mr-2 text-green-600" />
            ) : (
              <WifiOff className="h-5 w-5 mr-2 text-amber-600" />
            )}
            <div className="flex-1">
              <AlertTitle
                className={isOnline ? "text-green-800" : "text-amber-800"}
              >
                {isOnline ? "Connection Restored" : "You're Offline"}
              </AlertTitle>
              <AlertDescription
                className={isOnline ? "text-green-700" : "text-amber-700"}
              >
                {isOnline
                  ? "Your internet connection has been restored. The app is now updating."
                  : "Please check your internet connection and try again. Some features may be unavailable while offline."}
              </AlertDescription>
            </div>
            <div className="flex flex-col gap-2">
              <Button
                size="sm"
                variant="outline"
                className={`${
                  isOnline
                    ? "border-green-200 text-green-700 hover:bg-green-100"
                    : "border-amber-200 text-amber-700 hover:bg-amber-100"
                }`}
                onClick={handleRetry}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-1 ${
                    showRetryAnimation ? "animate-spin" : ""
                  }`}
                />
                {isOnline ? "Refresh" : "Retry"}
              </Button>

              {!isOnline && (
                <Button
                  size="sm"
                  variant="outline"
                  className="border-amber-200 text-amber-700 hover:bg-amber-100"
                  onClick={goToOfflinePage}
                >
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Offline Mode
                </Button>
              )}
            </div>
          </div>
        </Alert>
      </motion.div>
    </AnimatePresence>
  );
}

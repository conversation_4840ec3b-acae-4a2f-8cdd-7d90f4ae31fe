import { createClient } from "@supabase/supabase-js";

// Fallback values in case environment variables are not loaded
const FALLBACK_SUPABASE_URL = "https://wclwxrilybnzkhvqzbmy.supabase.co";
const FALLBACK_SUPABASE_ANON_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndjbHd4cmlseWJuemtodnF6Ym15Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzA5NTksImV4cCI6MjA2MTI0Njk1OX0.MIARsz34RX0EftvwUkWIrEYQqE8VstxaCI31mjLhSHw";

// Try to get environment variables, fall back to hardcoded values if not available
const supabaseUrl = import.meta.env?.VITE_SUPABASE_URL || FALLBACK_SUPABASE_URL;
const supabaseAnonKey =
  import.meta.env?.VITE_SUPABASE_ANON_KEY || FALLBACK_SUPABASE_ANON_KEY;

// Supabase client configuration

// Create a custom fetch function that handles SSL errors in development
const customFetch = (...args: Parameters<typeof fetch>) => {
  // Only use this in development mode
  if (import.meta.env.DEV) {
    const [resource, config] = args;

    // Don't use no-cors mode for auth endpoints as it breaks authentication
    const url = resource.toString();
    const isAuthEndpoint =
      url.includes("/auth/") || url.includes("grant_type=password");

    if (isAuthEndpoint) {
      return fetch(resource, config).catch((error) => {
        console.error("Auth fetch error:", error);
        throw error; // Re-throw auth errors
      });
    }

    // For non-auth endpoints, try with regular fetch first
    return fetch(resource, config).catch((error) => {
      console.error("Fetch error:", error, "URL:", url);

      // If regular fetch fails, try with credentials: 'include'
      return fetch(resource, {
        ...config,
        credentials: "include",
        // Add cache control to avoid caching issues
        cache: "no-cache",
        // Add mode to avoid CORS issues
        mode: "cors",
      }).catch((secondError) => {
        console.error("Second fetch attempt failed:", secondError);
        throw secondError;
      });
    });
  }

  // Use regular fetch in production
  return fetch(...args);
};

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    // Use implicit flow in development to avoid CORS issues
    flowType: import.meta.env.DEV ? "implicit" : "pkce",
    debug: import.meta.env.DEV, // Enable debug logs only in development
  },
  global: {
    headers: {
      "X-Client-Info": "attendance-tracking-system",
    },
    // Use custom fetch in development
    fetch: customFetch,
  },
  db: {
    schema: "public",
  },
  // Increase timeout for development
  realtime: {
    timeout: 60000, // 60 seconds
  },
  // Add retries for network issues
  httpClient: {
    retry: {
      retryInterval: 1000, // 1 second
      maxRetryCount: 3,
    },
  },
});

export type Database = {
  public: {
    Tables: {
      attendance_records: {
        Row: {
          id: string;
          student_id: string;
          room_id: string;
          timestamp: string;
          device_info: string;
          verification_method: string;
          status: string;
          location: any;
          created_at: string;
        };
        Insert: {
          id?: string;
          student_id: string;
          room_id: string;
          timestamp?: string;
          device_info: string;
          verification_method: string;
          status: string;
          location: any;
          created_at?: string;
        };
        Update: {
          id?: string;
          student_id?: string;
          room_id?: string;
          timestamp?: string;
          device_info?: string;
          verification_method?: string;
          status?: string;
          location?: any;
          created_at?: string;
        };
      };
      biometric_credentials: {
        Row: {
          id: string;
          user_id: string;
          credential_id: string;
          public_key: string;
          counter: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          credential_id: string;
          public_key: string;
          counter?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          credential_id?: string;
          public_key?: string;
          counter?: number;
          created_at?: string;
        };
      };
      profiles: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          email: string;
          role: string;
          photo_url?: string;
          student_id?: string;
          teacher_id?: string;
          admin_id?: string;
          department?: string;
          position?: string;
          subject?: string;
          course?: string;
          biometric_registered?: boolean;
          block_name?: string;
          room_number?: string;
          pin?: string;
          created_at?: string;
          updated_at?: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          email: string;
          role: string;
          photo_url?: string;
          student_id?: string;
          teacher_id?: string;
          admin_id?: string;
          department?: string;
          position?: string;
          subject?: string;
          course?: string;
          biometric_registered?: boolean;
          block_name?: string;
          room_number?: string;
          pin?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          email?: string;
          role?: string;
          photo_url?: string;
          student_id?: string;
          teacher_id?: string;
          admin_id?: string;
          department?: string;
          position?: string;
          subject?: string;
          course?: string;
          biometric_registered?: boolean;
          block_name?: string;
          room_number?: string;
          pin?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      // ... other tables ...
    };
  };
};

// Configure tables to receive real-time updates
async function configureRealtimeForAttendance() {
  try {
    // Initialize realtime subscription for attendance records
    await supabase.from("attendance_records").select("id").limit(1);
  } catch (error) {
    console.error(
      "Failed to configure realtime for attendance tracking:",
      error
    );
  }
}

// Check Supabase connection and provide user-friendly error message
export async function checkSupabaseConnection() {
  try {
    // Try to make a simple query to check connection
    const { data, error } = await supabase
      .from("block_locations")
      .select("id")
      .limit(1);

    if (error) {
      console.error("Supabase connection error:", error);
      return {
        connected: false,
        error: error.message,
        details: error.details || "No additional details available",
      };
    }

    return { connected: true };
  } catch (error) {
    console.error("Failed to connect to Supabase:", error);
    return {
      connected: false,
      error: error instanceof Error ? error.message : "Unknown error",
      details:
        "This might be due to network issues or the Supabase project being paused/deleted.",
    };
  }
}

// Initialize realtime configuration
configureRealtimeForAttendance();

export type Tables = Database["public"]["Tables"];
export type TablesInsert = Database["public"]["Tables"];

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  Plus,
  Trash2,
  CheckCircle,
  XCircle,
  Fingerprint,
  AlertTriangle,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { isWebAuthnAvailable, isPasskeyAvailable } from "@/lib/webauthn";
import { supabase } from "@/integrations/supabase/client";
import SimpleBiometricAuth from "@/components/auth/SimpleBiometricAuth";

export default function SimpleBiometricSettings() {
  const [hasRegistration, setHasRegistration] = useState(false);
  const [showRegistration, setShowRegistration] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isSupported, setIsSupported] = useState(false);

  const { user, profile, updateProfile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Load biometric data on mount
  useEffect(() => {
    const loadBiometricData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Check if WebAuthn is supported
        const supported = isWebAuthnAvailable();
        setIsSupported(supported);

        if (supported) {
          // Check if user has registered biometrics
          const hasPasskey = await isPasskeyAvailable(user.id);
          setHasRegistration(hasPasskey);
        }
      } catch (error) {
        console.error("Error loading biometric data:", error);
        toast({
          title: t("common.error"),
          description: t("biometrics.loadError"),
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadBiometricData();
  }, [user, toast]);

  // Handle registration success
  const handleRegistrationSuccess = async () => {
    setShowRegistration(false);
    setHasRegistration(true);

    // Update the profile to reflect biometric registration
    if (profile) {
      try {
        await updateProfile({ biometricRegistered: true });
      } catch (error) {
        console.error(
          "Error updating profile after biometric registration:",
          error
        );
      }
    }

    toast({
      title: t("common.success"),
      description: t("biometrics.registrationSuccess"),
    });
  };

  // Handle registration error
  const handleRegistrationError = (error: string) => {
    toast({
      title: t("biometrics.registrationFailed"),
      description: error,
      variant: "destructive",
    });
  };

  // Remove biometric registration
  const handleRemove = async () => {
    if (!user) return;

    try {
      // Delete from biometric_credentials table (the working table)
      const { error } = await supabase
        .from("biometric_credentials")
        .delete()
        .eq("user_id", user.id);

      if (error) throw error;

      // Update the profiles table to mark biometric as not registered
      const { error: profileError } = await supabase
        .from("profiles")
        .update({ biometric_registered: false })
        .eq("user_id", user.id);

      if (profileError) {
        console.error("Error updating profile biometric status:", profileError);
        // Don't throw here as the credential was successfully deleted
      }

      setHasRegistration(false);

      // Update the profile to reflect biometric removal
      if (profile) {
        try {
          await updateProfile({ biometricRegistered: false });
        } catch (error) {
          console.error(
            "Error updating profile after biometric removal:",
            error
          );
        }
      }

      toast({
        title: t("common.success"),
        description: t("biometrics.removalSuccess"),
      });
    } catch (error) {
      toast({
        title: t("common.error"),
        description: t("biometrics.removalError"),
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Shield className="w-8 h-8 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-600">{t("biometrics.loadingSettings")}...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {t("biometrics.biometricAuthentication")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 mx-auto text-orange-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t("biometrics.notAvailable")}
            </h3>
            <p className="text-gray-600 mb-4">
              {t("biometrics.deviceNotSupported")}
            </p>
            <div className="bg-blue-50 p-4 rounded-lg text-left">
              <p className="text-sm font-medium text-blue-800 mb-2">
                {t("biometrics.requirements")}
              </p>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• {t("biometrics.modernBrowser")}</li>
                <li>• {t("biometrics.httpsConnection")}</li>
                <li>• {t("biometrics.deviceWithSensor")}</li>
                <li>• {t("biometrics.webauthnSupport")}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {t("biometrics.biometricAuthentication")}
          </CardTitle>
          <p className="text-sm text-gray-600">
            {t("biometrics.setupBiometric")}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Status */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <div
                className={`p-2 rounded-lg ${
                  hasRegistration ? "bg-green-100" : "bg-gray-100"
                }`}
              >
                <Fingerprint
                  className={`w-5 h-5 ${
                    hasRegistration ? "text-green-600" : "text-gray-600"
                  }`}
                />
              </div>
              <div>
                <p className="font-medium">{t("biometrics.biometricAuthentication")}</p>
                <p className="text-sm text-gray-600">
                  {hasRegistration
                    ? t("biometrics.useFingerprintOrFaceAuth")
                    : t("biometrics.notRegisteredSetup")}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasRegistration ? (
                <>
                  <Badge
                    variant="outline"
                    className="text-green-600 border-green-200"
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {t("biometrics.active")}
                  </Badge>
                  <Button
                    onClick={handleRemove}
                    variant="outline"
                    size="sm"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </>
              ) : (
                <Button
                  onClick={() => setShowRegistration(true)}
                  size="sm"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 font-medium"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  {t("biometrics.register")}
                </Button>
              )}
            </div>
          </div>

          {/* Biometric Registration Interface - Show right after register button */}
          {showRegistration && user && profile && (
            <div className="border-2 border-blue-200 rounded-lg p-1 bg-gradient-to-r from-blue-50 to-purple-50">
              <Card className="border-0 shadow-none bg-transparent">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Fingerprint className="w-5 h-5 text-blue-600" />
                    {t("biometrics.registerBiometricAuth")}
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    {t("biometrics.followPromptsToSetup")}
                  </p>
                </CardHeader>
                <CardContent>
                  <SimpleBiometricAuth
                    userId={user.id}
                    username={profile.name || profile.email}
                    mode="register"
                    onSuccess={handleRegistrationSuccess}
                    onError={handleRegistrationError}
                    className="border-0 shadow-none"
                  />
                  <div className="mt-4">
                    <Button
                      onClick={() => setShowRegistration(false)}
                      variant="outline"
                      className="w-full"
                    >
                      {t("biometrics.cancel")}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Security Notice */}
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-green-800 mb-1">
                  {t("biometrics.securityAndPrivacy")}
                </p>
                <ul className="text-green-700 space-y-1">
                  <li>• {t("biometrics.biometricDataNeverLeaves")}</li>
                  <li>• {t("biometrics.onlyEncryptedTemplates")}</li>
                  <li>• {t("biometrics.removeAnytime")}</li>
                  <li>• {t("biometrics.pinBackupAvailable")}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 mb-1">
                  {t("biometrics.benefitsOfBiometric")}
                </p>
                <ul className="text-blue-700 space-y-1">
                  <li>• {t("biometrics.quickAttendanceMarking")}</li>
                  <li>• {t("biometrics.enhancedSecurity")}</li>
                  <li>• {t("biometrics.noNeedToRemember")}</li>
                  <li>• {t("biometrics.worksWithSensors")}</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

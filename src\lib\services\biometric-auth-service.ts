import { supabase } from "@/lib/supabase";
import { startAuthentication, isWebAuthnAvailable } from "@/lib/webauthn";

export interface BiometricSignInResult {
  success: boolean;
  user?: any;
  session?: any;
  error?: string;
}

/**
 * Service for handling biometric authentication and sign-in
 */
export class BiometricAuthService {
  /**
   * Check if a user has biometric authentication enabled
   */
  static async hasBiometricAuth(email: string): Promise<boolean> {
    try {
      // Use RPC function to bypass RLS for public biometric checking
      const { data, error } = await supabase.rpc('check_biometric_auth', {
        user_email: email
      });

      if (error) {
        // Fallback to direct query (might fail due to RLS)
        try {
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('biometric_registered')
            .eq('email', email)
            .single();

          if (profileError || !profile) {
            return false;
          }

          return profile.biometric_registered === true;
        } catch (fallbackError) {
          return false;
        }
      }

      return data === true;
    } catch (error) {
      console.error('Error checking biometric auth:', error);
      return false;
    }
  }

  /**
   * Perform biometric authentication and create session
   */
  static async signInWithBiometrics(email: string): Promise<BiometricSignInResult> {
    try {
      // Check if WebAuthn is available
      if (!isWebAuthnAvailable()) {
        return {
          success: false,
          error: 'Biometric authentication is not supported on this device or browser'
        };
      }

      // Get user profile using RPC to bypass RLS
      const { data: profileData, error: profileError } = await supabase.rpc('get_user_for_biometric_auth', {
        user_email: email
      });

      if (profileError) {
        return {
          success: false,
          error: 'Failed to fetch user profile'
        };
      }

      if (!profileData) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      if (!profileData.biometric_registered) {
        return {
          success: false,
          error: 'Biometric authentication not set up for this account'
        };
      }

      const profile = profileData;

      // Perform WebAuthn authentication
      await startAuthentication(profile.user_id);

      // Create a temporary biometric session token
      const sessionToken = await this.createBiometricSessionToken(profile.user_id, email);

      // Store the session token for verification
      localStorage.setItem('biometric_session_token', sessionToken);
      localStorage.setItem('biometric_user_email', email);

      return {
        success: true,
        user: null, // Will be set after session verification
        session: null,
        error: 'biometric_verification_complete' // Special flag for UI
      };

    } catch (error: any) {
      console.error('Biometric sign-in error:', error);

      let errorMessage = 'Biometric authentication failed';

      if (error.message?.includes('User verification')) {
        errorMessage = 'Biometric verification failed. Please try again.';
      } else if (error.message?.includes('not found')) {
        errorMessage = 'No biometric credentials found. Please register biometrics first.';
      } else if (error.message?.includes('timeout')) {
        errorMessage = 'Authentication timed out. Please try again.';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Complete biometric sign-in using stored session token
   */
  static async completeBiometricSignIn(): Promise<BiometricSignInResult> {
    try {
      const sessionToken = localStorage.getItem('biometric_session_token');
      const email = localStorage.getItem('biometric_user_email');

      if (!sessionToken || !email) {
        return {
          success: false,
          error: 'No biometric session found'
        };
      }

      // Verify the session token and sign in
      const isValid = await this.verifyBiometricSessionToken(sessionToken, email);

      if (!isValid) {
        return {
          success: false,
          error: 'Invalid biometric session'
        };
      }

      // Use magic link for seamless sign-in
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
          data: {
            biometric_auth: true,
            session_token: sessionToken
          }
        }
      });

      if (error) {
        throw error;
      }

      // Clean up stored tokens
      localStorage.removeItem('biometric_session_token');
      localStorage.removeItem('biometric_user_email');

      return {
        success: true,
        user: null, // Will be set by auth state change
        session: null
      };

    } catch (error: any) {
      return {
        success: false,
        error: 'Failed to complete biometric sign-in'
      };
    }
  }

  /**
   * Create a temporary session token for biometric authentication
   */
  private static async createBiometricSessionToken(userId: string, email: string): Promise<string> {
    // Generate a secure temporary token
    const token = crypto.randomUUID();
    const timestamp = Date.now();
    const expiresAt = timestamp + (5 * 60 * 1000); // 5 minutes

    // Create a signed token with user info
    const tokenData = {
      userId,
      email,
      timestamp,
      expiresAt,
      nonce: crypto.randomUUID()
    };

    // In a real implementation, you'd sign this with a secret key
    // For now, we'll use a simple encoding
    const encodedToken = btoa(JSON.stringify(tokenData));

    return `biometric_${encodedToken}`;
  }

  /**
   * Verify a biometric session token
   */
  private static async verifyBiometricSessionToken(token: string, email: string): Promise<boolean> {
    try {
      if (!token.startsWith('biometric_')) {
        return false;
      }

      const encodedData = token.replace('biometric_', '');
      const tokenData = JSON.parse(atob(encodedData));

      // Check expiry
      if (Date.now() > tokenData.expiresAt) {
        return false;
      }

      // Check email match
      if (tokenData.email !== email) {
        return false;
      }

      // Verify user still exists and has biometric enabled
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('user_id, biometric_registered')
        .eq('email', email)
        .eq('user_id', tokenData.userId)
        .single();

      if (error || !profile || !profile.biometric_registered) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error verifying biometric session token:', error);
      return false;
    }
  }

  /**
   * Alternative approach using magic link for biometric users
   */
  private static async createMagicLinkSession(email: string, profile: any): Promise<BiometricSignInResult> {
    try {
      // Create a one-time use magic link
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
          data: {
            biometric_auth: true,
            user_id: profile.user_id
          }
        }
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        user: null, // Will be set after magic link verification
        session: null
      };

    } catch (error: any) {
      return {
        success: false,
        error: 'Failed to create authentication session'
      };
    }
  }

  /**
   * Get all users with biometric authentication enabled (for admin purposes)
   */
  static async getBiometricUsers(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('user_id, email, name, role, biometric_registered')
        .eq('biometric_registered', true);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching biometric users:', error);
      return [];
    }
  }

  /**
   * Validate if biometric authentication is properly set up
   */
  static async validateBiometricSetup(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('biometric_credentials')
        .select('credential_id')
        .eq('user_id', userId)
        .single();

      return !error && !!data;
    } catch (error) {
      return false;
    }
  }
}

export default BiometricAuthService;

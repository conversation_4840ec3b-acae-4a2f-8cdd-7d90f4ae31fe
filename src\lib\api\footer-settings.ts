import { supabase } from "@/lib/supabase";

export interface FooterSettings {
  id: string;
  developer_name: string;
  developer_website: string;
  contact_email: string;
  app_tagline: string;
  github_url: string | null;
  linkedin_url: string | null;
  twitter_url: string | null;
  facebook_url: string | null;
  instagram_url: string | null;
  youtube_url: string | null;
  whatsapp_url: string | null;
  show_github: boolean;
  show_linkedin: boolean;
  show_twitter: boolean;
  show_facebook: boolean;
  show_instagram: boolean;
  show_youtube: boolean;
  show_whatsapp: boolean;
  show_developer_info: boolean;
  show_copyright: boolean;
  show_app_info: boolean;
  show_contact: boolean;
  show_legal: boolean;
  custom_copyright_text: string | null;
  privacy_policy_url: string | null;
  terms_of_service_url: string | null;
  cookie_policy_url: string | null;
  created_at: string;
  updated_at: string;
}

export interface FooterSettingsInput {
  developer_name?: string;
  developer_website?: string;
  contact_email?: string;
  app_tagline?: string;
  github_url?: string | null;
  linkedin_url?: string | null;
  twitter_url?: string | null;
  facebook_url?: string | null;
  instagram_url?: string | null;
  youtube_url?: string | null;
  whatsapp_url?: string | null;
  show_github?: boolean;
  show_linkedin?: boolean;
  show_twitter?: boolean;
  show_facebook?: boolean;
  show_instagram?: boolean;
  show_youtube?: boolean;
  show_whatsapp?: boolean;
  show_developer_info?: boolean;
  show_copyright?: boolean;
  show_app_info?: boolean;
  show_contact?: boolean;
  show_legal?: boolean;
  custom_copyright_text?: string | null;
  privacy_policy_url?: string | null;
  terms_of_service_url?: string | null;
  cookie_policy_url?: string | null;
}

/**
 * Get the footer settings
 * @returns The footer settings
 */
export const getFooterSettings = async (): Promise<FooterSettings | null> => {
  try {
    console.log("Fetching footer settings from database...");

    // First, check if the table exists
    try {
      const { data: tableExists, error: tableCheckError } = await supabase.rpc(
        "check_table_exists",
        { table_name: "footer_settings" }
      );

      if (tableCheckError) {
        console.error(
          "Error checking if footer_settings table exists:",
          tableCheckError
        );
        console.error("Error details:", JSON.stringify(tableCheckError));
      } else {
        console.log("Table exists check result:", tableExists);
      }
    } catch (checkError) {
      console.error("Exception checking if table exists:", checkError);
    }

    // Now try to fetch the data
    const { data, error } = await supabase
      .from("footer_settings")
      .select("*")
      .limit(1)
      .single();

    if (error) {
      console.error("Error fetching footer settings:", error);
      console.error("Error details:", JSON.stringify(error));

      // If the error is that the table doesn't exist, try to create it
      if (error.code === "42P01") {
        // relation does not exist
        console.log("Table doesn't exist, returning default settings");
        return getDefaultFooterSettings();
      }

      return getDefaultFooterSettings();
    }

    console.log("Footer settings fetched successfully:", data);
    return data;
  } catch (error) {
    console.error("Error fetching footer settings:", error);
    console.error(
      "Error details:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    return getDefaultFooterSettings();
  }
};

/**
 * Get default footer settings when database fails
 * @returns Default footer settings
 */
const getDefaultFooterSettings = (): FooterSettings => {
  console.log("Using default footer settings");
  return {
    id: "default",
    developer_name: "Attendance Tracking System Team",
    developer_website: "https://attendancetracking.edu",
    contact_email: "<EMAIL>",
    app_tagline:
      "Streamlining attendance management for educational institutions",
    github_url: "https://github.com",
    linkedin_url: "https://linkedin.com",
    twitter_url: "https://twitter.com",
    facebook_url: "https://facebook.com",
    instagram_url: "https://instagram.com",
    youtube_url: "https://youtube.com",
    whatsapp_url: "https://wa.me/1234567890",
    show_github: true,
    show_linkedin: true,
    show_twitter: true,
    show_facebook: true,
    show_instagram: true,
    show_youtube: true,
    show_whatsapp: true,
    show_developer_info: true,
    show_copyright: true,
    show_app_info: true,
    show_contact: true,
    show_legal: true,
    custom_copyright_text: null,
    privacy_policy_url: null,
    terms_of_service_url: null,
    cookie_policy_url: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
};

/**
 * Update the footer settings
 * @param settings The new footer settings
 * @returns The updated footer settings
 */
export const updateFooterSettings = async (
  settings: FooterSettingsInput
): Promise<FooterSettings | null> => {
  try {
    console.log("Updating footer settings with:", settings);

    // First, get the current settings to get the ID
    const currentSettings = await getFooterSettings();
    console.log("Current settings:", currentSettings);

    if (!currentSettings || currentSettings.id === "default") {
      console.log("No existing settings found, creating new record");
      // If no settings exist, create a new record
      const { data, error } = await supabase
        .from("footer_settings")
        .insert({
          ...settings,
          updated_at: new Date().toISOString(),
        })
        .select("*")
        .single();

      if (error) {
        console.error("Error creating footer settings:", error);
        console.error("Error details:", JSON.stringify(error));
        return null;
      }

      console.log("Successfully created footer settings:", data);
      return data;
    }

    console.log("Updating existing settings with ID:", currentSettings.id);
    // Update existing settings
    const { data, error } = await supabase
      .from("footer_settings")
      .update({
        ...settings,
        updated_at: new Date().toISOString(),
      })
      .eq("id", currentSettings.id)
      .select("*")
      .single();

    if (error) {
      console.error("Error updating footer settings:", error);
      console.error("Error details:", JSON.stringify(error));
      return null;
    }

    console.log("Successfully updated footer settings:", data);
    return data;
  } catch (error) {
    console.error("Error updating footer settings:", error);
    console.error(
      "Error details:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    return null;
  }
};

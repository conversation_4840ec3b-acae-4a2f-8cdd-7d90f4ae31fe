import sgMail from "@sendgrid/mail";
import { supabase } from "@/lib/supabase";

// Type definitions for service configurations
interface EmailServiceConfig {
  apiKey: string;
  fromEmail: string;
  enabled: boolean;
}

interface SMSServiceConfig {
  accountSid: string;
  authToken: string;
  phoneNumber: string;
  enabled: boolean;
}

// Cache for service configurations to avoid repeated database queries
let emailConfigCache: EmailServiceConfig | null = null;
let smsConfigCache: SMSServiceConfig | null = null;
let lastConfigFetch = 0;
const CONFIG_CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Helper function to create the system_settings table if it doesn't exist
 */
const ensureSystemSettingsTable = async (): Promise<void> => {
  try {
    // First, try to check if the table already exists
    try {
      // This will throw an error if the table doesn't exist
      await supabase
        .from("system_settings")
        .select("count(*)", { count: "exact", head: true });
      // If we get here, the table exists
      return;
    } catch (checkError) {
      // Table might not exist, continue to creation
      console.log("Checking system_settings table:", checkError);
    }

    // Try to create the table using RPC
    try {
      await supabase.rpc("execute_sql", {
        sql: `
          CREATE TABLE IF NOT EXISTS system_settings (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            setting_name TEXT NOT NULL UNIQUE,
            setting_value JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
          );

          -- Add RLS policies if they don't exist
          DO $$
          BEGIN
            IF NOT EXISTS (
              SELECT 1 FROM pg_policies
              WHERE tablename = 'system_settings' AND policyname = 'Admins can view system settings'
            ) THEN
              ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

              -- Only admins can view system settings
              CREATE POLICY "Admins can view system settings" ON system_settings
                FOR SELECT
                USING (auth.jwt() ->> 'role' = 'admin');

              -- Only admins can insert system settings
              CREATE POLICY "Admins can insert system settings" ON system_settings
                FOR INSERT
                WITH CHECK (auth.jwt() ->> 'role' = 'admin');

              -- Only admins can update system settings
              CREATE POLICY "Admins can update system settings" ON system_settings
                FOR UPDATE
                USING (auth.jwt() ->> 'role' = 'admin');

              -- Only admins can delete system settings
              CREATE POLICY "Admins can delete system settings" ON system_settings
                FOR DELETE
                USING (auth.jwt() ->> 'role' = 'admin');
            END IF;
          END
          $$;
        `,
      });
    } catch (rpcError) {
      console.error(
        "Error using RPC to create system_settings table:",
        rpcError
      );

      // If RPC fails, try a direct insert to see if the table exists
      try {
        // Try to insert a dummy record - this will fail if the table doesn't exist
        // but we'll catch that error
        const { error } = await supabase.from("system_settings").upsert({
          setting_name: "test_setting",
          setting_value: JSON.stringify({ test: true }),
          updated_at: new Date().toISOString(),
        });

        if (error) {
          console.error("Error testing system_settings table:", error);
        } else {
          // If insert succeeded, delete the test record
          await supabase
            .from("system_settings")
            .delete()
            .eq("setting_name", "test_setting");
        }
      } catch (insertError) {
        console.error(
          "Error testing system_settings table with insert:",
          insertError
        );
      }
    }
  } catch (error) {
    console.error("Error ensuring system_settings table exists:", error);
    // Continue anyway, as we've tried our best to create or verify the table
  }
};

/**
 * Fetch email service configuration from environment variables or database
 */
export const getEmailServiceConfig =
  async (): Promise<EmailServiceConfig | null> => {
    const now = Date.now();

    // Return cached config if it's still valid
    if (emailConfigCache && now - lastConfigFetch < CONFIG_CACHE_TTL) {
      return emailConfigCache;
    }

    try {
      // Initialize variables
      let apiKey = "";
      let fromEmail = "";
      let enabled = false;

      // First try to get from database (primary storage)
      try {
        const { data, error } = await supabase
          .from("system_settings")
          .select("*")
          .eq("setting_name", "email_service_config")
          .single();

        if (!error && data && data.setting_value) {
          const config = JSON.parse(data.setting_value);
          apiKey = config.apiKey;
          fromEmail = config.fromEmail;
          enabled = config.enabled !== undefined ? config.enabled : true; // Default to enabled if not specified
          console.log("Email config loaded from database");
        } else if (error) {
          console.error(
            "Error fetching email service config from database:",
            error
          );
          // Fall back to other methods
        }
      } catch (dbError) {
        console.error("Database error fetching email config:", dbError);
        // Fall back to other methods
      }

      // If not found in database, try localStorage
      if (!apiKey || !fromEmail) {
        try {
          const storedConfig = localStorage.getItem("email_service_config");
          if (storedConfig) {
            const config = JSON.parse(storedConfig);
            apiKey = config.apiKey;
            fromEmail = config.fromEmail;
            enabled = config.enabled !== undefined ? config.enabled : true; // Default to enabled if not specified
            console.log("Email config loaded from localStorage");
          }
        } catch (localStorageError) {
          console.error("Error reading from localStorage:", localStorageError);
        }
      }

      // If still not found, try environment variables
      if (!apiKey || !fromEmail) {
        apiKey = import.meta.env.VITE_SENDGRID_API_KEY || apiKey;
        fromEmail = import.meta.env.VITE_EMAIL_FROM || fromEmail;
        // Environment variables don't store enabled state, default to true
        enabled = true;

        if (apiKey && fromEmail) {
          console.log("Email config loaded from environment variables");
        }
      }

      // Provide default fromEmail if nothing else is available
      if (!fromEmail) {
        fromEmail = "<EMAIL>";
      }

      // Update cache if we have valid config
      if (apiKey && fromEmail) {
        emailConfigCache = { apiKey, fromEmail, enabled };
        lastConfigFetch = now;
        return emailConfigCache;
      }

      return null;
    } catch (error) {
      console.error("Error getting email service config:", error);
      return null;
    }
  };

/**
 * Fetch SMS service configuration from environment variables or database
 */
export const getSMSServiceConfig =
  async (): Promise<SMSServiceConfig | null> => {
    const now = Date.now();

    // Return cached config if it's still valid
    if (smsConfigCache && now - lastConfigFetch < CONFIG_CACHE_TTL) {
      return smsConfigCache;
    }

    try {
      // Initialize variables
      let accountSid = "";
      let authToken = "";
      let phoneNumber = "";
      let enabled = false;

      // First try to get from database (primary storage)
      try {
        const { data, error } = await supabase
          .from("system_settings")
          .select("*")
          .eq("setting_name", "sms_service_config")
          .single();

        if (!error && data && data.setting_value) {
          const config = JSON.parse(data.setting_value);
          accountSid = config.accountSid;
          authToken = config.authToken;
          phoneNumber = config.phoneNumber;
          enabled = config.enabled !== undefined ? config.enabled : false; // Default to disabled for SMS
          console.log("SMS config loaded from database");
        } else if (error) {
          console.error(
            "Error fetching SMS service config from database:",
            error
          );
          // Fall back to other methods
        }
      } catch (dbError) {
        console.error("Database error fetching SMS config:", dbError);
        // Fall back to other methods
      }

      // If not found in database, try localStorage
      if (!accountSid || !authToken || !phoneNumber) {
        try {
          const storedConfig = localStorage.getItem("sms_service_config");
          if (storedConfig) {
            const config = JSON.parse(storedConfig);
            accountSid = config.accountSid;
            authToken = config.authToken;
            phoneNumber = config.phoneNumber;
            enabled = config.enabled !== undefined ? config.enabled : false; // Default to disabled for SMS
            console.log("SMS config loaded from localStorage");
          }
        } catch (localStorageError) {
          console.error("Error reading from localStorage:", localStorageError);
        }
      }

      // If still not found, try environment variables
      if (!accountSid || !authToken || !phoneNumber) {
        accountSid = import.meta.env.VITE_TWILIO_ACCOUNT_SID || accountSid;
        authToken = import.meta.env.VITE_TWILIO_AUTH_TOKEN || authToken;
        phoneNumber = import.meta.env.VITE_TWILIO_PHONE_NUMBER || phoneNumber;
        // Environment variables don't store enabled state, default to false for SMS
        enabled = false;

        if (accountSid && authToken && phoneNumber) {
          console.log("SMS config loaded from environment variables");
        }
      }

      // Update cache if we have valid config
      if (accountSid && authToken && phoneNumber) {
        smsConfigCache = { accountSid, authToken, phoneNumber, enabled };
        lastConfigFetch = now;
        return smsConfigCache;
      }

      return null;
    } catch (error) {
      console.error("Error getting SMS service config:", error);
      return null;
    }
  };

/**
 * Validate email service configuration by testing API key
 */
export const validateEmailServiceConfig = async (
  config: EmailServiceConfig
): Promise<{ valid: boolean; error?: string }> => {
  try {
    // Call the Edge Function to validate configuration
    const { data, error } = await supabase.functions.invoke(
      "send-notification",
      {
        body: {
          action: "validate",
          type: "email",
          data: config,
        },
      }
    );

    if (error) {
      console.error("Error validating email config:", error);
      return { valid: false, error: error.message };
    }

    return data;
  } catch (error: any) {
    console.error("Error validating email config:", error);
    return { valid: false, error: error.message };
  }
};

/**
 * Validate SMS service configuration by testing credentials
 */
export const validateSMSServiceConfig = async (
  config: SMSServiceConfig
): Promise<{ valid: boolean; error?: string }> => {
  try {
    // Call the Edge Function to validate configuration
    const { data, error } = await supabase.functions.invoke(
      "send-notification",
      {
        body: {
          action: "validate",
          type: "sms",
          data: config,
        },
      }
    );

    if (error) {
      console.error("Error validating SMS config:", error);
      return { valid: false, error: error.message };
    }

    return data;
  } catch (error: any) {
    console.error("Error validating SMS config:", error);
    return { valid: false, error: error.message };
  }
};

/**
 * Save email service configuration to the database with validation
 */
export const saveEmailServiceConfig = async (
  config: EmailServiceConfig
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Validate configuration before saving if enabled
    if (config.enabled && config.apiKey && config.fromEmail) {
      const validation = await validateEmailServiceConfig(config);
      if (!validation.valid) {
        return {
          success: false,
          error: `Configuration validation failed: ${validation.error}`
        };
      }
    }

    // Save to database (primary storage method)
    try {
      // First check if the record already exists
      const { data: existingData } = await supabase
        .from("system_settings")
        .select("id")
        .eq("setting_name", "email_service_config")
        .single();

      let error;

      if (existingData && existingData.id) {
        // Update existing record
        const { error: updateError } = await supabase
          .from("system_settings")
          .update({
            setting_value: JSON.stringify(config),
            updated_at: new Date().toISOString(),
          })
          .eq("setting_name", "email_service_config");

        error = updateError;
      } else {
        // Insert new record
        const { error: insertError } = await supabase
          .from("system_settings")
          .insert({
            setting_name: "email_service_config",
            setting_value: JSON.stringify(config),
            updated_at: new Date().toISOString(),
          });

        error = insertError;
      }

      if (!error) {
        // Update cache
        emailConfigCache = config;
        lastConfigFetch = Date.now();
        console.log("Email config saved to database");
        return { success: true };
      } else {
        console.error("Error saving email service config to database:", error);
        return { success: false, error: error.message };
      }
    } catch (dbError: any) {
      console.error("Database error saving email config:", dbError);
      return { success: false, error: dbError.message };
    }
  } catch (error: any) {
    console.error("Error saving email service config:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Save SMS service configuration to the database
 */
export const saveSMSServiceConfig = async (
  config: SMSServiceConfig
): Promise<boolean> => {
  try {
    // Save to database (primary storage method)
    try {
      // First check if the record already exists
      const { data: existingData } = await supabase
        .from("system_settings")
        .select("id")
        .eq("setting_name", "sms_service_config")
        .single();

      let error;

      if (existingData && existingData.id) {
        // Update existing record
        const { error: updateError } = await supabase
          .from("system_settings")
          .update({
            setting_value: JSON.stringify(config),
            updated_at: new Date().toISOString(),
          })
          .eq("setting_name", "sms_service_config");

        error = updateError;
      } else {
        // Insert new record
        const { error: insertError } = await supabase
          .from("system_settings")
          .insert({
            setting_name: "sms_service_config",
            setting_value: JSON.stringify(config),
            updated_at: new Date().toISOString(),
          });

        error = insertError;
      }

      if (!error) {
        // Update cache
        smsConfigCache = config;
        lastConfigFetch = Date.now();
        console.log("SMS config saved to database");
        return true;
      } else {
        console.error("Error saving SMS service config to database:", error);
        // Fall back to localStorage
      }
    } catch (dbError) {
      console.error("Database error saving SMS config:", dbError);
      // Fall back to localStorage
    }

    // Store in localStorage as a fallback
    try {
      localStorage.setItem("sms_service_config", JSON.stringify(config));

      // Update cache
      smsConfigCache = config;
      lastConfigFetch = Date.now();

      console.log("SMS config saved to localStorage (fallback)");
      return true;
    } catch (localStorageError) {
      console.error("Error saving to localStorage:", localStorageError);
      return false;
    }
  } catch (error) {
    console.error("Error saving SMS service config:", error);
    return false;
  }
};

/**
 * Send an email using SendGrid via Supabase Edge Function
 */
export const sendEmailWithSendGrid = async (
  to: string,
  subject: string,
  message: string
): Promise<boolean> => {
  try {
    // Get email configuration
    const config = await getEmailServiceConfig();

    // Check if email service is enabled
    if (!config || !config.enabled) {
      console.log("Email service is disabled. Skipping email send.");
      return false;
    }

    // Call the Supabase Edge Function to send the email
    const { data, error } = await supabase.functions.invoke(
      "send-notification",
      {
        body: {
          type: "email",
          data: {
            to,
            subject,
            message,
          },
        },
      }
    );

    if (error) {
      console.error("Error calling send-notification function:", error);
      return false;
    }

    if (data && data.success) {
      console.log("Email sent successfully via Edge Function");
      return true;
    } else {
      console.error("Email sending failed:", data?.message || "Unknown error");
      return false;
    }
  } catch (error: any) {
    console.error("Error sending email with SendGrid:", error);
    return false;
  }
};

/**
 * Send an SMS using Twilio via Supabase Edge Function
 */
export const sendSMSWithTwilio = async (
  to: string,
  message: string
): Promise<boolean> => {
  try {
    // Get SMS configuration
    const config = await getSMSServiceConfig();

    // Check if SMS service is enabled
    if (!config || !config.enabled) {
      console.log("SMS service is disabled. Skipping SMS send.");
      return false;
    }

    // Call the Supabase Edge Function to send the SMS
    const { data, error } = await supabase.functions.invoke(
      "send-notification",
      {
        body: {
          type: "sms",
          data: {
            to,
            message,
          },
        },
      }
    );

    if (error) {
      console.error("Error calling send-notification function:", error);
      return false;
    }

    if (data && data.success) {
      console.log("SMS sent successfully via Edge Function");
      return true;
    } else {
      console.error("SMS sending failed:", data?.message || "Unknown error");
      return false;
    }
  } catch (error) {
    console.error("Error sending SMS with Twilio:", error);
    return false;
  }
};

/**
 * Test the email service configuration
 */
export const testEmailService = async (to: string): Promise<boolean> => {
  try {
    return await sendEmailWithSendGrid(
      to,
      "Campus Guardian - Email Service Test",
      "This is a test email from Campus Guardian Attendance System. If you received this email, the email service is configured correctly."
    );
  } catch (error) {
    console.error("Error testing email service:", error);
    return false;
  }
};

/**
 * Test the SMS service configuration
 */
export const testSMSService = async (to: string): Promise<boolean> => {
  try {
    return await sendSMSWithTwilio(
      to,
      "Campus Guardian - This is a test SMS from Campus Guardian Attendance System. If you received this message, the SMS service is configured correctly."
    );
  } catch (error) {
    console.error("Error testing SMS service:", error);
    return false;
  }
};

import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/lib/supabase";
import { Excuse } from "@/lib/types";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/lib/utils/toast";
import { useTranslation } from "react-i18next";
import { notifyParents } from "@/lib/services/notification-service";
import { useDataCache } from "./useDataCache";

interface UseExcusesOptions {
  role?: "student" | "teacher" | "admin";
  studentId?: string;
  roomId?: string;
  status?: "pending" | "approved" | "rejected" | "all";
  limit?: number;
}

export function useExcuses(options: UseExcusesOptions = {}) {
  const { profile } = useAuth();
  const { t } = useTranslation();
  const userId = profile?.id || "guest";
  const cacheKey = `excuses-${userId}-${
    options.role || profile?.role || "guest"
  }-${options.studentId || ""}-${options.roomId || ""}-${
    options.status || "all"
  }`;

  // Create a fetch function that we can pass to useDataCache
  const fetchExcusesData = useCallback(async () => {
    if (!profile) {
      throw new Error("User not authenticated");
    }

    let query = supabase
      .from("excuses")
      .select(
        `
        *,
        student:profiles!excuses_student_id_fkey(id, name),
        room:rooms!excuses_room_id_fkey(id, name),
        teacher:profiles!excuses_teacher_id_fkey(id, name)
      `
      )
      .order("start_date", { ascending: false });

    // Apply filters based on role and options
    if (options.role === "student" || profile.role === "student") {
      // Students can only see their own excuses
      query = query.eq("student_id", options.studentId || profile.id);
    } else if (options.role === "teacher" || profile.role === "teacher") {
      // Teachers can see excuses for their rooms
      if (options.roomId) {
        query = query.eq("room_id", options.roomId);
      } else if (options.studentId) {
        query = query.eq("student_id", options.studentId);
      }
    }

    // Filter by status if provided
    if (options.status && options.status !== "all") {
      query = query.eq("status", options.status);
    }

    // Apply limit if provided
    if (options.limit) {
      query = query.limit(options.limit);
    }

    const { data, error } = await query;

    if (error) throw error;

    // Transform the data to match our Excuse interface
    const formattedExcuses: Excuse[] = data.map((excuse) => ({
      ...excuse,
      studentName: excuse.student?.name,
      roomName: excuse.room?.name,
      teacherName: excuse.teacher?.name,
    }));

    return formattedExcuses;
  }, [
    profile,
    options.role,
    options.studentId,
    options.roomId,
    options.status,
    options.limit,
  ]);

  // Use our data cache hook
  const { data, loading, error, refetch } = useDataCache<Excuse[]>(
    fetchExcusesData,
    {
      cacheKey,
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      initialFetch: true,
    }
  );

  // Alias for refetch to maintain API compatibility
  const fetchExcuses = async () => {
    await refetch();
  };

  const createExcuse = async (
    excuseData: Omit<
      Excuse,
      | "id"
      | "created_at"
      | "updated_at"
      | "status"
      | "studentName"
      | "roomName"
      | "teacherName"
    >
  ) => {
    try {
      if (!profile) {
        throw new Error("User not authenticated");
      }

      // Check if the student already has a pending excuse
      if (profile.role === "student") {
        const { data: existingExcuses, error: checkError } = await supabase
          .from("excuses")
          .select("id")
          .eq("student_id", excuseData.student_id || profile.id)
          .eq("status", "pending");

        if (checkError) throw checkError;

        if (existingExcuses && existingExcuses.length > 0) {
          throw new Error(
            "You already have a pending excuse request. Please delete it before submitting a new one."
          );
        }
      }

      const { data, error } = await supabase
        .from("excuses")
        .insert({
          ...excuseData,
          student_id: excuseData.student_id || profile.id,
          status: "pending",
        })
        .select();

      if (error) throw error;

      // Get student name for notification
      const { data: studentData, error: studentError } = await supabase
        .from("profiles")
        .select("name")
        .eq("id", excuseData.student_id || profile.id)
        .single();

      if (studentError) {
        console.error("Error fetching student name:", studentError);
      }

      // Get room name for notification
      const { data: roomData, error: roomError } = await supabase
        .from("rooms")
        .select("name")
        .eq("id", excuseData.room_id)
        .single();

      if (roomError) {
        console.error("Error fetching room name:", roomError);
      }

      // Send notification to parents
      try {
        // Get school information for enhanced templates
        const { data: schoolData } = await supabase
          .from("system_settings")
          .select("setting_value")
          .eq("setting_name", "parent_notification_settings")
          .single();

        let schoolInfo = {
          schoolName: "Your School Name",
          contactEmail: "<EMAIL>",
          schoolPolicy: "Please contact the school office for attendance policy information."
        };

        if (schoolData) {
          try {
            const parsedSchoolInfo = JSON.parse(schoolData.setting_value);
            schoolInfo = {
              schoolName: parsedSchoolInfo.school_name || schoolInfo.schoolName,
              contactEmail: parsedSchoolInfo.contact_email || schoolInfo.contactEmail,
              schoolPolicy: parsedSchoolInfo.school_policy || schoolInfo.schoolPolicy
            };
          } catch (parseError) {
            console.warn("Error parsing school info:", parseError);
          }
        }

        const notificationResult = await notifyParents(
          excuseData.student_id || profile.id,
          {
            subject: "Absence Request Submitted",
            message: "Your child has submitted an absence request",
            studentName: studentData?.name || "Your child",
            excuseId: data[0].id,
            startDate: excuseData.start_date,
            endDate: excuseData.end_date,
            reason: excuseData.reason,
            schoolName: schoolInfo.schoolName,
            contactEmail: schoolInfo.contactEmail,
            schoolPolicy: schoolInfo.schoolPolicy,
            templateType: "new", // Specify template type for new submissions
          }
        );

        if (notificationResult.success) {
          console.log("Parent notification sent successfully");
        } else {
          console.log(
            "Parent notification status:",
            notificationResult.message
          );
        }
      } catch (notifyError) {
        console.error("Error sending parent notification:", notifyError);
        // Don't throw here, we still want to create the excuse even if notification fails
      }

      toast.translateSuccess(
        t,
        "student.excuses.submitted",
        "student.excuses.submittedDescription"
      );

      // Refresh the excuses list
      fetchExcuses();

      return data[0];
    } catch (err: any) {
      console.error("Error creating excuse:", err);
      toast.error(
        t("common.error"),
        {
          description: t("student.excuses.submitError", { error: err.message })
        }
      );
      throw err;
    }
  };

  const updateExcuseStatus = async (
    excuseId: string,
    status: "approved" | "rejected",
    notes?: string
  ) => {
    try {
      if (!profile) {
        throw new Error("User not authenticated");
      }

      // First, get the excuse details
      const { data: excuseData, error: excuseError } = await supabase
        .from("excuses")
        .select("*")
        .eq("id", excuseId)
        .single();

      if (excuseError) throw excuseError;

      // Update the excuse status
      const { data, error } = await supabase
        .from("excuses")
        .update({
          status,
          notes,
          teacher_id: profile.id,
          updated_at: new Date().toISOString(),
        })
        .eq("id", excuseId)
        .select();

      if (error) throw error;

      // If the excuse is approved, update attendance records for the date range
      if (status === "approved") {
        // Get the date range
        const startDate = new Date(excuseData.start_date);
        const endDate = new Date(excuseData.end_date);

        // Create an array of dates between start and end date
        const dateRange: string[] = [];
        const currentDate = new Date(startDate);

        while (currentDate <= endDate) {
          // Use timezone-safe date formatting
          const year = currentDate.getFullYear();
          const month = String(currentDate.getMonth() + 1).padStart(2, '0');
          const day = String(currentDate.getDate()).padStart(2, '0');
          dateRange.push(`${year}-${month}-${day}`);
          currentDate.setDate(currentDate.getDate() + 1);
        }

        // For each date in the range, check if there's an attendance record
        for (const date of dateRange) {
          // Check if there's an existing attendance record for this date
          const { data: attendanceData, error: attendanceError } =
            await supabase
              .from("attendance_records")
              .select("*")
              .eq("student_id", excuseData.student_id)
              .eq("room_id", excuseData.room_id)
              .gte("timestamp", `${date}T00:00:00`)
              .lt("timestamp", `${date}T23:59:59`)
              .order("timestamp", { ascending: false });

          if (attendanceError) {
            console.error(
              "Error checking attendance records:",
              attendanceError
            );
            continue;
          }

          // If there's an existing record with status 'absent', update it to 'excused'
          if (attendanceData && attendanceData.length > 0) {
            const absentRecords = attendanceData.filter(
              (record) => record.status === "absent"
            );

            for (const record of absentRecords) {
              const { error: updateError } = await supabase
                .from("attendance_records")
                .update({
                  status: "excused",
                  verification_method: "manual",
                  updated_at: new Date().toISOString(),
                })
                .eq("id", record.id);

              if (updateError) {
                console.error("Error updating attendance record:", updateError);
              }
            }
          } else {
            // If no record exists, create a new 'excused' record
            const { error: insertError } = await supabase
              .from("attendance_records")
              .insert({
                student_id: excuseData.student_id,
                room_id: excuseData.room_id,
                timestamp: `${date}T${excuseData.start_time}`,
                status: "excused",
                verification_method: "manual",
                device_info: `Excused by ${
                  profile.name || "teacher"
                } (ID: ${excuseId})`,
              });

            if (insertError) {
              console.error("Error creating attendance record:", insertError);
            }
          }
        }
      }

      // Get student name for notification
      const { data: studentData, error: studentError } = await supabase
        .from("profiles")
        .select("name")
        .eq("id", excuseData.student_id)
        .single();

      if (studentError) {
        console.error("Error fetching student name:", studentError);
      }

      // Get room name for notification
      const { data: roomData, error: roomError } = await supabase
        .from("rooms")
        .select("name")
        .eq("id", excuseData.room_id)
        .single();

      if (roomError) {
        console.error("Error fetching room name:", roomError);
      }

      // Send notification to parents about the status update
      try {
        // Get school information for enhanced templates
        const { data: schoolData } = await supabase
          .from("system_settings")
          .select("setting_value")
          .eq("setting_name", "parent_notification_settings")
          .single();

        let schoolInfo = {
          schoolName: "Your School Name",
          contactEmail: "<EMAIL>",
          schoolPolicy: "Please contact the school office for attendance policy information."
        };

        if (schoolData) {
          try {
            const parsedSchoolInfo = JSON.parse(schoolData.setting_value);
            schoolInfo = {
              schoolName: parsedSchoolInfo.school_name || schoolInfo.schoolName,
              contactEmail: parsedSchoolInfo.contact_email || schoolInfo.contactEmail,
              schoolPolicy: parsedSchoolInfo.school_policy || schoolInfo.schoolPolicy
            };
          } catch (parseError) {
            console.warn("Error parsing school info:", parseError);
          }
        }

        const notificationResult = await notifyParents(excuseData.student_id, {
          subject: `Absence Request ${
            status === "approved" ? "Approved" : "Rejected"
          }`,
          message: `Your child's absence request has been ${
            status === "approved" ? "approved" : "rejected"
          }`,
          studentName: studentData?.name || "Your child",
          excuseId: excuseId,
          startDate: excuseData.start_date,
          endDate: excuseData.end_date,
          reason: excuseData.reason,
          schoolName: schoolInfo.schoolName,
          contactEmail: schoolInfo.contactEmail,
          schoolPolicy: schoolInfo.schoolPolicy,
          templateType: status === "approved" ? "approved" : "rejected", // Specify correct template type
        });

        if (notificationResult.success) {
          console.log("Parent notification sent successfully");
        } else {
          console.log(
            "Parent notification status:",
            notificationResult.message
          );
        }
      } catch (notifyError) {
        console.error("Error sending parent notification:", notifyError);
        // Don't throw here, we still want to update the excuse even if notification fails
      }

      toast.success(
        t(`teacher.excuses.${status === "approved" ? "approved" : "rejected"}`),
        {
          description: t(`teacher.excuses.${status === "approved" ? "approvedDescription" : "rejectedDescription"}`)
        }
      );

      // Refresh the excuses list
      fetchExcuses();

      return data[0];
    } catch (err: any) {
      console.error(
        `Error ${status === "approved" ? "approving" : "rejecting"} excuse:`,
        err
      );
      toast.error(
        t("common.error"),
        {
          description: t("teacher.excuses.updateError", { error: err.message })
        }
      );
      throw err;
    }
  };

  const deleteExcuse = async (excuseId: string) => {
    try {
      if (!profile) {
        throw new Error("User not authenticated");
      }

      // Only allow deletion of pending excuses by the student who created them
      const { error } = await supabase
        .from("excuses")
        .delete()
        .eq("id", excuseId)
        .eq("student_id", profile.id)
        .eq("status", "pending");

      if (error) throw error;

      toast.translateSuccess(
        t,
        "student.excuses.deleted",
        "student.excuses.deletedDescription"
      );

      // Refresh the excuses list
      fetchExcuses();
    } catch (err: any) {
      console.error("Error deleting excuse:", err);
      toast.error(
        t("common.error"),
        {
          description: t("student.excuses.deleteError", { error: err.message })
        }
      );
      throw err;
    }
  };

  // Set up real-time subscription for excuse changes
  useEffect(() => {
    if (!profile) return;

    // Subscribe to changes in the excuses table
    const channel = supabase
      .channel("excuses-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "excuses",
          filter:
            profile.role === "student"
              ? `student_id=eq.${profile.id}`
              : undefined,
        },
        (payload) => {
          console.log("Excuse change detected:", payload);
          // Refresh data when changes occur
          refetch();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [profile, refetch]);

  return {
    excuses: data || [],
    loading,
    error: error ? new Error(error) : null,
    fetchExcuses,
    createExcuse,
    updateExcuseStatus,
    deleteExcuse,
  };
}

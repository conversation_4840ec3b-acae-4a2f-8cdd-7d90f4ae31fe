import { supabase } from "@/lib/supabase";
import { getDeviceFingerprint } from "@/lib/utils/security";

/**
 * Action types for audit logging
 */
export enum AuditActionType {
  // User actions
  USER_LOGIN = "user_login",
  USER_LOGOUT = "user_logout",
  USER_CREATED = "user_created",
  USER_UPDATED = "user_updated",
  USER_DELETED = "user_deleted",
  
  // School actions
  SCHOOL_CREATED = "school_created",
  SCHOOL_UPDATED = "school_updated",
  SCHOOL_DELETED = "school_deleted",
  SCHOOL_SETTINGS_UPDATED = "school_settings_updated",
  
  // Invitation code actions
  INVITATION_CODE_GENERATED = "invitation_code_generated",
  INVITATION_CODE_USED = "invitation_code_used",
  
  // Attendance actions
  ATTENDANCE_RECORDED = "attendance_recorded",
  ATTENDANCE_UPDATED = "attendance_updated",
  ATTENDANCE_DELETED = "attendance_deleted",
  
  // Security actions
  SECURITY_ALERT = "security_alert",
  PERMISSION_CHANGED = "permission_changed",
  
  // Data actions
  DATA_EXPORTED = "data_exported",
  DATA_IMPORTED = "data_imported",
}

/**
 * Entity types for audit logging
 */
export enum AuditEntityType {
  USER = "user",
  SCHOOL = "school",
  STUDENT = "student",
  TEACHER = "teacher",
  ADMIN = "admin",
  ATTENDANCE = "attendance",
  ROOM = "room",
  BLOCK = "block",
  EXCUSE = "excuse",
  NOTIFICATION = "notification",
  SETTINGS = "settings",
}

/**
 * Interface for audit log entry
 */
interface AuditLogEntry {
  school_id?: string;
  user_id?: string;
  action_type: AuditActionType;
  entity_type: AuditEntityType;
  entity_id?: string;
  details?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

/**
 * Log an action to the audit logs
 * @param entry The audit log entry to record
 * @returns Promise that resolves when the log is recorded
 */
export const logAuditEvent = async (entry: AuditLogEntry): Promise<void> => {
  try {
    // Get device info
    const deviceInfo = getDeviceFingerprint();
    
    // Insert audit log
    const { error } = await supabase.from("audit_logs").insert({
      ...entry,
      user_agent: deviceInfo.userAgent,
      created_at: new Date().toISOString(),
    });
    
    if (error) {
      console.error("Error logging audit event:", error);
    }
  } catch (error) {
    console.error("Error in audit logging:", error);
  }
};

/**
 * Get audit logs for a school
 * @param schoolId The school ID to get logs for
 * @param limit The maximum number of logs to return
 * @param offset The offset for pagination
 * @returns Promise that resolves to the audit logs
 */
export const getSchoolAuditLogs = async (
  schoolId: string,
  limit = 50,
  offset = 0
): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from("audit_logs")
      .select(`
        *,
        users:user_id (
          email
        )
      `)
      .eq("school_id", schoolId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      console.error("Error fetching audit logs:", error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error("Error in getSchoolAuditLogs:", error);
    return [];
  }
};

/**
 * Get system-wide audit logs (for system admins)
 * @param limit The maximum number of logs to return
 * @param offset The offset for pagination
 * @returns Promise that resolves to the audit logs
 */
export const getSystemAuditLogs = async (
  limit = 50,
  offset = 0
): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from("audit_logs")
      .select(`
        *,
        users:user_id (
          email
        ),
        schools:school_id (
          name
        )
      `)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      console.error("Error fetching system audit logs:", error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error("Error in getSystemAuditLogs:", error);
    return [];
  }
};

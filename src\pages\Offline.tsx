import { useState, useEffect } from "react";
import { WifiOff, Refresh<PERSON>w, <PERSON>Left } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";

export default function Offline() {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // Redirect to home page when back online
      window.location.href = "/";
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  const handleRetry = () => {
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    // Simulate checking connection
    setTimeout(() => {
      if (navigator.onLine) {
        window.location.reload();
      } else {
        setIsRetrying(false);
      }
    }, 2000);
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-2 border-amber-200 shadow-lg">
          <CardHeader className="pb-4">
            <div className="w-full flex justify-center mb-4">
              <div className="bg-amber-100 p-4 rounded-full">
                <WifiOff className="h-12 w-12 text-amber-600" />
              </div>
            </div>
            <CardTitle className="text-center text-2xl">You're Offline</CardTitle>
            <CardDescription className="text-center text-base">
              We can't connect to the server right now
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-amber-50 p-4 rounded-lg text-amber-800 text-sm">
              <p className="font-medium mb-2">Troubleshooting tips:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Check your internet connection</li>
                <li>Try connecting to a different network</li>
                <li>Restart your router if possible</li>
                <li>Check if other apps can connect to the internet</li>
              </ul>
            </div>
            
            {retryCount > 2 && (
              <div className="bg-blue-50 p-4 rounded-lg text-blue-800 text-sm">
                <p>
                  If you continue to experience issues, some features may still be
                  available in offline mode. You can also try again later when your
                  connection improves.
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <Button 
              className="w-full bg-amber-600 hover:bg-amber-700" 
              onClick={handleRetry}
              disabled={isRetrying}
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Checking connection...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try again
                </>
              )}
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full border-amber-200 text-amber-700 hover:bg-amber-50"
              onClick={handleGoBack}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go back
            </Button>
          </CardFooter>
        </Card>
        
        <p className="text-center text-gray-500 text-sm mt-4">
          Retry count: {retryCount} • Connection status: {isOnline ? "Online" : "Offline"}
        </p>
      </motion.div>
    </div>
  );
}

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Ban, AlertTriangle, LogOut } from "lucide-react";
import { useAuth } from "@/context/AuthContext";

interface BlockedUserPageProps {
  userName?: string;
}

const BlockedUserPage: React.FC<BlockedUserPageProps> = ({ userName }) => {
  const { signOut } = useAuth();

  const handleSignOut = async () => {
    await signOut();
    window.location.href = "/";
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md shadow-lg border-red-200">
        <CardHeader className="text-center border-b pb-4">
          <div className="flex justify-center mb-4">
            <div className="h-20 w-20 rounded-full bg-red-100 flex items-center justify-center">
              <Ban className="h-10 w-10 text-red-500" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-red-600">Account Blocked</CardTitle>
        </CardHeader>
        <CardContent className="pt-6 text-center space-y-4">
          <p className="text-lg">
            {userName ? `Hello ${userName}, ` : ""}Your account has been temporarily blocked.
          </p>
          <div className="bg-amber-50 p-4 rounded-md border border-amber-200 flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-amber-800">
              If you believe this is a mistake, please contact your administrator for assistance.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center pt-2 pb-6">
          <Button variant="outline" className="w-full" onClick={handleSignOut}>
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default BlockedUserPage;

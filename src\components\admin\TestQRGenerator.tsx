import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import QRCode from "react-qr-code";
import { generateSecureQRCode } from "@/lib/utils/qr-security";

export default function TestQRGenerator() {
  const [qrData, setQrData] = useState<string>("");

  const generateTestQR = () => {
    // Generate a test QR code with sample data
    const testQRData = generateSecureQRCode(
      "test-room-123", // room_id
      "test-school-456", // school_id  
      "test-block-789", // block_id
      5 // 5 minutes expiry
    );

    const qrString = JSON.stringify(testQRData);
    setQrData(qrString);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Test QR Generator</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={generateTestQR} className="w-full">
          Generate Test QR Code
        </Button>
        
        {qrData && (
          <div className="space-y-4">
            <div className="border-4 border-primary p-4 rounded-md bg-white">
              <QRCode
                value={qrData}
                size={200}
                style={{ height: "auto", maxWidth: "100%", width: "100%" }}
              />
            </div>
            
            <div>
              <p className="text-sm font-medium mb-2">QR Data:</p>
              <textarea
                value={JSON.stringify(JSON.parse(qrData), null, 2)}
                readOnly
                className="w-full p-2 border rounded-md font-mono text-xs h-32 resize-none"
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * 🔧 QR Debug Test Page
 * Test page to debug QR expiry time configuration
 */

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getQRExpirySeconds } from "@/lib/services/qr-security-api";
import { generateSecureQRCode } from "@/lib/services/qr-security-api";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/lib/utils/toast";
import { useTranslation } from "react-i18next";
import QRCode from "react-qr-code";

export default function QRDebugTest() {
  const [envDebug, setEnvDebug] = useState<any>({});
  const [qrData, setQrData] = useState<string>("");
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const { profile } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    // Debug environment variables
    const debug = {
      VITE_QR_EXPIRY_SECONDS: import.meta.env.VITE_QR_EXPIRY_SECONDS,
      NEXT_PUBLIC_QR_EXPIRY_SECONDS: import.meta.env.NEXT_PUBLIC_QR_EXPIRY_SECONDS,
      getQRExpirySeconds: getQRExpirySeconds(),
      allEnvVars: import.meta.env,
    };
    
    console.log("🔧 Environment Debug:", debug);
    setEnvDebug(debug);
  }, []);

  // Timer for countdown
  useEffect(() => {
    if (qrExpiry) {
      const interval = setInterval(() => {
        const remaining = Math.max(0, Math.floor((qrExpiry.getTime() - Date.now()) / 1000));
        setTimeLeft(remaining);
        
        if (remaining <= 0) {
          clearInterval(interval);
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [qrExpiry]);

  const generateTestQR = async () => {
    try {
      if (!profile?.school_id) {
        toast({
          title: "Error",
          description: "No school ID found",
          variant: "destructive",
        });
        return;
      }

      const expirySeconds = getQRExpirySeconds();
      console.log(`🔄 Generating test QR with expiry: ${expirySeconds} seconds`);

      const qrResult = await generateSecureQRCode(
        "test-room-id",
        profile.school_id,
        "test-block-id",
        expirySeconds,
        profile.id
      );

      const expiry = new Date(qrResult.expires_at);
      const qrString = JSON.stringify(qrResult);
      
      setQrData(qrString);
      setQrExpiry(expiry);
      setTimeLeft(Math.floor((expiry.getTime() - Date.now()) / 1000));

      console.log(`✅ QR generated with expiry: ${expiry.toISOString()}`);
      console.log(`⏰ Time until expiry: ${Math.floor((expiry.getTime() - Date.now()) / 1000)} seconds`);

      toast.success(
        t("debug.qr.testGenerated"),
        {
          description: t("debug.qr.expiresIn", { seconds: expirySeconds })
        }
      );
    } catch (error) {
      console.error("❌ Error generating test QR:", error);
      toast.error(
        t("common.error"),
        {
          description: error instanceof Error ? error.message : t("debug.qr.generateFailed")
        }
      );
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>🔧 QR Expiry Debug Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={generateTestQR} size="lg">
              Generate Test QR Code
            </Button>
          </CardContent>
        </Card>

        {/* Environment Debug */}
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Environment Variables Debug</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">VITE_QR_EXPIRY_SECONDS</h4>
                <Badge variant="outline" className="font-mono">
                  {envDebug.VITE_QR_EXPIRY_SECONDS || "undefined"}
                </Badge>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">NEXT_PUBLIC_QR_EXPIRY_SECONDS</h4>
                <Badge variant="outline" className="font-mono">
                  {envDebug.NEXT_PUBLIC_QR_EXPIRY_SECONDS || "undefined"}
                </Badge>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">getQRExpirySeconds() Result</h4>
                <Badge variant="outline" className="font-mono">
                  {envDebug.getQRExpirySeconds} seconds
                </Badge>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Expected Expiry</h4>
                <Badge variant="outline" className="font-mono">
                  {Math.round(envDebug.getQRExpirySeconds / 60)} minutes
                </Badge>
              </div>
            </div>

            <details className="mt-4">
              <summary className="cursor-pointer font-medium">All Environment Variables</summary>
              <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(envDebug.allEnvVars, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>

        {/* QR Code Display */}
        {qrData && (
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Generated QR Code</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex flex-col items-center">
                  <div className="border-4 border-primary p-4 rounded-md bg-white">
                    <QRCode
                      value={qrData}
                      size={200}
                      style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                    />
                  </div>
                  
                  <div className="mt-4 text-center">
                    <div className="text-lg font-bold">
                      Time Left: {formatTime(timeLeft)}
                    </div>
                    <div className="text-sm text-gray-600">
                      Expires at: {qrExpiry?.toLocaleTimeString()}
                    </div>
                  </div>
                </div>

                <div className="flex-1">
                  <h4 className="font-medium mb-2">QR Data Analysis</h4>
                  <div className="space-y-2 text-sm">
                    {(() => {
                      try {
                        const parsed = JSON.parse(qrData);
                        const createdAt = new Date(parsed.timestamp);
                        const expiresAt = new Date(parsed.expires_at);
                        const actualDuration = Math.floor((expiresAt.getTime() - createdAt.getTime()) / 1000);
                        
                        return (
                          <div className="space-y-2">
                            <div>
                              <strong>Created:</strong> {createdAt.toLocaleTimeString()}
                            </div>
                            <div>
                              <strong>Expires:</strong> {expiresAt.toLocaleTimeString()}
                            </div>
                            <div>
                              <strong>Actual Duration:</strong> 
                              <Badge className={actualDuration === envDebug.getQRExpirySeconds ? "ml-2 bg-green-100 text-green-800" : "ml-2 bg-red-100 text-red-800"}>
                                {actualDuration} seconds
                              </Badge>
                            </div>
                            <div>
                              <strong>Expected Duration:</strong> 
                              <Badge className="ml-2 bg-blue-100 text-blue-800">
                                {envDebug.getQRExpirySeconds} seconds
                              </Badge>
                            </div>
                            <div>
                              <strong>Match:</strong> 
                              <Badge className={actualDuration === envDebug.getQRExpirySeconds ? "ml-2 bg-green-100 text-green-800" : "ml-2 bg-red-100 text-red-800"}>
                                {actualDuration === envDebug.getQRExpirySeconds ? "✅ Correct" : "❌ Mismatch"}
                              </Badge>
                            </div>
                          </div>
                        );
                      } catch (error) {
                        return <div className="text-red-600">Error parsing QR data</div>;
                      }
                    })()}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { usePersistentTabState } from "@/hooks/useStatePersistence";
import { useTabVisibility } from "@/hooks/useTabVisibility";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import Navbar from "@/components/shared/Navbar";
import QRGenerator from "@/components/admin/QRGenerator";
import TabletSetupHelper from "@/components/admin/TabletSetupHelper";
import UserManagement from "@/components/admin/UserManagement";
import AdminAlerts from "@/components/admin/AdminAlerts";
import EnhancedFraudDetection from "@/components/admin/EnhancedFraudDetection";
import AdminStudentDirectory from "@/components/admin/AdminStudentDirectory";
import AdminSettings from "@/components/admin/AdminSettings";
import AdminExcusesManagement from "@/components/admin/AdminExcusesManagement";
import AdminParentContacts from "@/pages/AdminParentContacts";
import SchoolManagement from "@/components/admin/SchoolManagement";
import SchoolContext from "@/components/admin/SchoolContext";
import SchoolSettings from "@/components/admin/SchoolSettings";
import AuditLogs from "@/components/admin/AuditLogs";
import { Admin as AdminType } from "@/lib/types";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { TabProvider } from "@/context/TabContext";
import DashboardMessage from "@/components/shared/DashboardMessage";
import Footer from "@/components/shared/Footer";
import { motion, AnimatePresence } from "framer-motion";
import FeedbackForm from "@/components/shared/FeedbackForm";
import SimpleCarousel from "@/components/shared/SimpleCarousel";
import { useTranslation } from "react-i18next";
import {
  Bell,
  Users,
  Settings,
  FileText,
  UserPlus,
  User,
  Building2,
  Shield,
  ArrowLeft,
  QrCode,
  Clock,
  Cog,
  Tablet,
  Zap,
} from "lucide-react";
import AdminProfile from "@/components/admin/AdminProfile";
import { TabletSetupQR } from "@/components/tablet/TabletSetupQR";
import AutomaticQRStatus from "@/components/admin/AutomaticQRStatus";

export default function Admin() {
  const { profile, loading } = useAuth();
  const { currentSchool } = useSchool();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const isTabVisible = useTabVisibility();

  // Use persistent tab state to prevent losing tab selection on page reload
  const [activeTab, setActiveTab] = usePersistentTabState(
    "admin_dashboard",
    "users"
  );

  // State for nested tabs
  const [activeSettingsTab, setActiveSettingsTab] = usePersistentTabState(
    "admin_settings_tab",
    "school-settings"
  );
  const [activeSecurityTab, setActiveSecurityTab] = usePersistentTabState(
    "admin_security_tab",
    "audit-logs"
  );

  // Prevent unnecessary operations when tab is not visible
  useEffect(() => {
    if (!isTabVisible) {
      // Tab is not visible, pause any heavy operations
      return;
    }

    // Tab is visible, resume normal operations
    console.log("Admin dashboard tab is now visible");
  }, [isTabVisible]);

  // If loading, show loading indicator
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <p>{t("common.loading")}</p>
        </div>
      </div>
    );
  }

  // If not an admin, redirect to login
  if (!profile || profile.role !== "admin") {
    navigate("/login");
    return null;
  }

  // Check if admin profile is incomplete
  const adminProfile = profile as AdminType;
  const isProfileIncomplete = !adminProfile.position || !adminProfile.school;

  // Check if user is a system admin (access_level 3)
  const isSystemAdmin = adminProfile.accessLevel === 3;

  return (
    <TabProvider defaultTab="users">
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1">
          {isSystemAdmin && (
            <div className="container mx-auto px-4 flex justify-start items-center pt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate("/system-admin")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("admin.backToSystemAdmin")}
              </Button>

              {isSystemAdmin && currentSchool && (
                <Badge
                  variant="outline"
                  className="bg-blue-100 text-blue-800 border-blue-200 ml-4"
                >
                  {t("admin.systemAdminView")}
                </Badge>
              )}
            </div>
          )}

          {isProfileIncomplete && (
            <div className="container mx-auto px-4 py-6">
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-yellow-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      {t("admin.profile.incompleteProfile")}
                      <a
                        href="/admin/profile"
                        className="font-medium underline text-yellow-700 hover:text-yellow-600 ml-2"
                      >
                        {t("profile.completeProfile")}
                      </a>
                      <span className="ml-2">{t("common.or")}</span>
                      <button
                        onClick={() =>
                          document
                            .querySelector('[value="profile"]')
                            ?.dispatchEvent(new MouseEvent("click"))
                        }
                        className="font-medium underline text-yellow-700 hover:text-yellow-600 ml-1"
                      >
                        {t("admin.profile.goToProfileTab")}
                      </button>
                    </p>
                  </div>
                </div>
              </div>

              <h1 className="text-2xl font-bold">
                {t("admin.dashboard.title")}
                {currentSchool && (
                  <span className="text-muted-foreground ml-2 font-normal">
                    {currentSchool.name}
                  </span>
                )}
              </h1>
            </div>
          )}

          {!isProfileIncomplete && (
            <>
              {/* Dashboard Carousel with title overlay - directly below navbar */}
              <SimpleCarousel userType="admin" />

              {/* Display dashboard message below carousel */}
              <div className="container mx-auto px-4 mt-6">
                <DashboardMessage userType="admin" />
              </div>
            </>
          )}

          {/* Floating feedback button */}
          {!isProfileIncomplete && <FeedbackForm variant="fab" />}

          <div className="container mx-auto px-4">
            <Tabs
              defaultValue={activeTab}
              onValueChange={(value) => setActiveTab(value)}
              className="w-full"
            >
              <Card className="mb-6 p-3 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border-0 shadow-lg">
                {/* Single row of organized tabs with beautiful styling */}
                <div
                  className="overflow-x-auto scrollbar-hide"
                  style={{
                    scrollbarWidth: "none",
                    msOverflowStyle: "none",
                    WebkitOverflowScrolling: "touch",
                  }}
                >
                  <TabsList className="bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm shadow-inner rounded-xl p-2 gap-2 flex flex-nowrap w-max min-w-full">
                    {/* Users Tab */}
                    <TabsTrigger
                      value="users"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-green-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <Users className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("admin.tabs.users")}
                      </span>
                    </TabsTrigger>

                    {/* Students Tab */}
                    <TabsTrigger
                      value="students"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <User className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("admin.tabs.students")}
                      </span>
                    </TabsTrigger>

                    {/* Settings Tab */}
                    <TabsTrigger
                      value="settings"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-gray-500 data-[state=active]:to-gray-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <Settings className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("admin.tabs.settings")}
                      </span>
                    </TabsTrigger>

                    {/* Security Tab */}
                    <TabsTrigger
                      value="security"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500 data-[state=active]:to-red-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <Shield className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("admin.tabs.security")}
                      </span>
                    </TabsTrigger>

                    {/* QR Codes Tab */}
                    <TabsTrigger
                      value="qrcodes"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-teal-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <QrCode className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("admin.tabs.qrCodes")}
                      </span>
                    </TabsTrigger>

                    {/* Tablets Tab */}
                    <TabsTrigger
                      value="tablets"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <Tablet className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        Tablets
                      </span>
                    </TabsTrigger>

                    {/* Excuses Tab */}
                    <TabsTrigger
                      value="excuses"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-orange-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <FileText className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("admin.tabs.excuses")}
                      </span>
                    </TabsTrigger>

                    {/* Parents Tab */}
                    <TabsTrigger
                      value="parents"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <UserPlus className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("admin.tabs.parents")}
                      </span>
                    </TabsTrigger>

                    {/* Profile Tab */}
                    <TabsTrigger
                      value="profile"
                      className="flex items-center justify-center gap-2 px-4 py-3 h-12 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-blue-700 data-[state=active]:text-white data-[state=active]:shadow-lg flex-shrink-0 min-w-[80px] sm:min-w-[120px]"
                    >
                      <User className="h-5 w-5" />
                      <span className="font-medium hidden sm:inline whitespace-nowrap">
                        {t("common.profile")}
                      </span>
                    </TabsTrigger>
                  </TabsList>
                </div>
              </Card>

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <TabsContent value="users" className="mt-0">
                    <div className="grid gap-6">
                      <SchoolContext />
                      <UserManagement />
                    </div>
                  </TabsContent>

                  <TabsContent value="students" className="mt-0">
                    <AdminStudentDirectory />
                  </TabsContent>

                  <TabsContent value="qrcodes" className="mt-0">
                    {/* Nested QR Codes Tabs */}
                    <Tabs
                      defaultValue="generator"
                      className="w-full"
                    >
                      <Card className="mb-4 p-2">
                        <TabsList className="grid w-full grid-cols-3 bg-gray-50 dark:bg-slate-700">
                          <TabsTrigger
                            value="generator"
                            className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 text-xs sm:text-sm"
                          >
                            <QrCode className="h-4 w-4" />
                            {t("admin.qrCodes.generator")}
                          </TabsTrigger>
                          <TabsTrigger
                            value="tablet"
                            className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 text-xs sm:text-sm"
                          >
                            <Tablet className="h-4 w-4" />
                            {t("admin.qrCodes.tabletSetup")}
                          </TabsTrigger>
                          <TabsTrigger
                            value="automatic"
                            className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 text-xs sm:text-sm"
                          >
                            <Zap className="h-4 w-4" />
                            {t("admin.qrCodes.automatic")}
                          </TabsTrigger>
                        </TabsList>
                      </Card>

                      <AnimatePresence mode="wait">
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          transition={{ duration: 0.2 }}
                        >
                          <TabsContent value="generator" className="mt-0">
                            <QRGenerator />
                          </TabsContent>

                          <TabsContent value="tablet" className="mt-0">
                            <TabletSetupHelper />
                          </TabsContent>

                          <TabsContent value="automatic" className="mt-0">
                            <AutomaticQRStatus />
                          </TabsContent>
                        </motion.div>
                      </AnimatePresence>
                    </Tabs>
                  </TabsContent>

                  <TabsContent value="tablets" className="mt-0">
                    <div className="space-y-6">
                      <div className="text-center mb-6">
                        <h2 className="text-2xl font-bold text-gray-800 mb-2">
                          📱 {t("admin.tablets.title")}
                        </h2>
                        <p className="text-gray-600">
                          {t("admin.tablets.subtitle")}
                        </p>
                      </div>
                      <TabletSetupQR />
                    </div>
                  </TabsContent>

                  <TabsContent value="excuses" className="mt-0">
                    <AdminExcusesManagement />
                  </TabsContent>

                  <TabsContent value="parents" className="mt-0">
                    <AdminParentContacts />
                  </TabsContent>

                  <TabsContent value="settings" className="mt-0">
                    {/* Nested Settings Tabs */}
                    <Tabs
                      defaultValue={activeSettingsTab}
                      onValueChange={(value) => setActiveSettingsTab(value)}
                      className="w-full"
                    >
                      <Card className="mb-4 p-2">
                        <TabsList className="grid w-full grid-cols-2 bg-gray-50 dark:bg-slate-700">
                          <TabsTrigger
                            value="school-settings"
                            className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600"
                          >
                            <Cog className="h-4 w-4" />
                            {t("admin.tabs.schoolSettings")}
                          </TabsTrigger>
                          <TabsTrigger
                            value="attendance-settings"
                            className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600"
                          >
                            <Clock className="h-4 w-4" />
                            {t("admin.tabs.attendanceSettings")}
                          </TabsTrigger>
                        </TabsList>
                      </Card>

                      <AnimatePresence mode="wait">
                        <motion.div
                          key={activeSettingsTab}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          transition={{ duration: 0.2 }}
                        >
                          <TabsContent value="school-settings" className="mt-0">
                            <div className="grid gap-6">
                              <SchoolContext />
                              <SchoolSettings />
                            </div>
                          </TabsContent>

                          <TabsContent
                            value="attendance-settings"
                            className="mt-0"
                          >
                            <AdminSettings />
                          </TabsContent>
                        </motion.div>
                      </AnimatePresence>
                    </Tabs>
                  </TabsContent>

                  <TabsContent value="security" className="mt-0">
                    {/* Nested Security Tabs */}
                    <Tabs
                      defaultValue={activeSecurityTab}
                      onValueChange={(value) => setActiveSecurityTab(value)}
                      className="w-full"
                    >
                      <Card className="mb-4 p-2">
                        <TabsList className="grid w-full grid-cols-2 bg-gray-50 dark:bg-slate-700">
                          <TabsTrigger
                            value="audit-logs"
                            className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600"
                          >
                            <Shield className="h-4 w-4" />
                            {t("admin.tabs.auditLogs")}
                          </TabsTrigger>
                          <TabsTrigger
                            value="fraud-detection"
                            className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600"
                          >
                            <Shield className="h-4 w-4" />
                            {t("admin.tabs.fraud")}
                          </TabsTrigger>
                        </TabsList>
                      </Card>

                      <AnimatePresence mode="wait">
                        <motion.div
                          key={activeSecurityTab}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          transition={{ duration: 0.2 }}
                        >
                          <TabsContent value="audit-logs" className="mt-0">
                            <div className="grid gap-6">
                              <SchoolContext />
                              <AuditLogs />
                            </div>
                          </TabsContent>

                          <TabsContent value="fraud-detection" className="mt-0">
                            <EnhancedFraudDetection />
                          </TabsContent>
                        </motion.div>
                      </AnimatePresence>
                    </Tabs>
                  </TabsContent>

                  <TabsContent value="profile" className="mt-0">
                    <AdminProfile />
                  </TabsContent>
                </motion.div>
              </AnimatePresence>
            </Tabs>
          </div>
        </div>
        {!isProfileIncomplete && <Footer />}
      </div>
    </TabProvider>
  );
}
